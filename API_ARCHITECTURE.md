# API Architecture Documentation

## Overview

This document describes the enhanced API architecture for the Somayya Academy Flutter application. The architecture is designed to be scalable, maintainable, and follows best practices for API integration with GetX state management.

## Architecture Components

### 1. Base API Service (`lib/services/api_service.dart`)

The `ApiService` is the core HTTP client that provides:
- Dio HTTP client configuration
- Authentication token management
- Automatic token refresh
- Standardized error handling
- Flavor-based configuration
- Enhanced logging for development

**Key Features:**
- Secure token storage using `flutter_secure_storage`
- Automatic Bearer token attachment
- Comprehensive error handling with custom exceptions
- Safe HTTP methods that return `ApiResponse<T>` wrappers

### 2. Feature-Based API Services

#### AuthApiService (`lib/app/services/auth_api_service.dart`)
Handles all authentication-related endpoints:
- User signin/signup
- OTP validation
- Password management
- Email verification
- Token refresh
- Session management

#### TopicApiService (`lib/app/services/topic_api_service.dart`)
Manages topic-related operations:
- CRUD operations for topics
- Topic search and filtering
- Category-based retrieval
- Popular topics
- User favorites

#### CourseApiService (`lib/app/services/course_api_service.dart`)
Handles course management:
- Course CRUD operations
- Course enrollment
- Progress tracking
- Search and filtering
- Popular and featured courses

### 3. API Models and Response Handling

#### ApiResponse<T> (`lib/app/models/api_response.dart`)
Standardized response wrapper that provides:
- Success/error status
- Typed data payload
- Error information
- Metadata support

#### PaginatedResponse<T>
Handles paginated API responses with:
- Items list
- Pagination metadata
- Navigation helpers

#### ApiError and ValidationError
Structured error handling for:
- Server errors
- Validation errors
- Network errors
- Authentication errors

### 4. Exception Handling (`lib/app/exceptions/api_exceptions.dart`)

Custom exception hierarchy:
- `NetworkException` - Connection issues
- `ServerException` - 5xx errors
- `ClientException` - 4xx errors
- `AuthException` - Authentication failures
- `ValidationException` - Form validation errors

### 5. Interceptors (`lib/app/interceptors/auth_interceptor.dart`)

#### AuthInterceptor
- Automatic token attachment
- Token refresh on 401 errors
- Request queuing during refresh
- Authentication failure handling

#### EnhancedLogInterceptor
- Formatted request/response logging
- Sensitive data filtering
- Development-only logging

## Usage Examples

### Basic API Call

```dart
// Get the API service
final authApiService = Get.find<AuthApiService>();

// Make an API call
final response = await authApiService.signin(
  email: '<EMAIL>',
  password: 'password123',
);

// Handle the response
if (response.success) {
  final userData = response.data;
  print('Login successful: ${userData?['name']}');
} else {
  print('Login failed: ${response.error?.message}');
}
```

### Handling Validation Errors

```dart
try {
  final response = await authApiService.signup(
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'weak',
  );
  
  if (!response.success && response.error != null) {
    if (response.error!.validationErrors != null) {
      for (final error in response.error!.validationErrors!) {
        print('${error.field}: ${error.message}');
      }
    }
  }
} catch (e) {
  if (e is ValidationException) {
    // Handle validation errors
    final passwordError = e.getFieldError('password');
    if (passwordError != null) {
      print('Password error: ${passwordError.message}');
    }
  }
}
```

### Paginated Data

```dart
final courseApiService = Get.find<CourseApiService>();

final response = await courseApiService.getAllCourses(
  page: 1,
  limit: 20,
  category: 'programming',
);

if (response.success && response.data != null) {
  final paginatedData = response.data!;
  print('Total courses: ${paginatedData.totalItems}');
  print('Current page: ${paginatedData.currentPage}');
  print('Has next page: ${paginatedData.hasNext}');
  
  for (final course in paginatedData.items) {
    print('Course: ${course['title']}');
  }
}
```

## Configuration

### API Constants (`lib/app/constants/api_constants.dart`)

All API endpoints are centralized in `ApiConstants`:

```dart
// Base URLs (configured per flavor)
static const String devBaseUrl = 'http://13.234.22.118:8098/somayya-academy/api/v1';
static const String prodBaseUrl = 'http://13.234.22.118:8098/somayya-academy/api/v1';

// Authentication endpoints
static const String signin = '/auth/signin';
static const String signup = '/auth/signup';
// ... more endpoints

// Dynamic endpoints
static String deleteCourse(String courseId) => '/courses/$courseId';
```

### Flavor Configuration

The API service automatically uses the correct base URL based on the current flavor:

```dart
// In AppConfig
static String get apiBaseUrl {
  switch (flavor) {
    case AppFlavor.dev:
      return ApiConstants.devBaseUrl;
    case AppFlavor.prod:
      return ApiConstants.prodBaseUrl;
  }
}
```

## Extending the Architecture

### Adding a New API Service

1. Create a new service class extending `GetxService`:

```dart
class NotificationApiService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  Future<ApiResponse<List<Map<String, dynamic>>>> getNotifications() async {
    return await _apiService.safeGet<List<Map<String, dynamic>>>(
      '/notifications',
      fromJson: (data) => (data as List)
          .map((item) => item as Map<String, dynamic>)
          .toList(),
    );
  }
}
```

2. Add endpoints to `ApiConstants`:

```dart
// Notification endpoints
static const String notificationsBase = '/notifications';
static const String getNotifications = '$notificationsBase';
static const String markAsRead = '$notificationsBase/mark-read';
```

3. Register in `InitialBinding`:

```dart
Get.put<NotificationApiService>(NotificationApiService(), permanent: true);
```

### Adding Custom Models

Create typed models for better type safety:

```dart
class Course {
  final String id;
  final String title;
  final String description;
  final double price;

  Course({required this.id, required this.title, required this.description, required this.price});

  factory Course.fromJson(Map<String, dynamic> json) => Course(
    id: json['id'],
    title: json['title'],
    description: json['description'],
    price: json['price']?.toDouble() ?? 0.0,
  );
}

// Usage
final response = await courseApiService.safeGet<List<Course>>(
  '/courses',
  fromJson: (data) => (data as List).map((item) => Course.fromJson(item)).toList(),
);
```

## Best Practices

1. **Always use the safe methods** (`safeGet`, `safePost`, etc.) for consistent error handling
2. **Handle both success and error cases** in your UI controllers
3. **Use typed responses** when possible for better type safety
4. **Centralize endpoint definitions** in `ApiConstants`
5. **Follow the feature-based service pattern** for organization
6. **Use proper error handling** with try-catch blocks
7. **Test API calls** with both success and failure scenarios

## Testing

The architecture supports easy testing by:
- Dependency injection through GetX
- Mockable service interfaces
- Standardized response formats
- Isolated error handling

Example test setup:

```dart
void main() {
  setUp(() {
    Get.put<ApiService>(MockApiService());
    Get.put<AuthApiService>(AuthApiService());
  });

  test('should handle successful login', () async {
    // Test implementation
  });
}
```

## Integration with Existing Controllers

### Updating AuthController

```dart
class AuthController extends GetxController {
  final AuthApiService _authApiService = Get.find<AuthApiService>();

  Rxn<User> user = Rxn<User>();
  RxBool loading = false.obs;

  Future<void> login(String email, String password) async {
    loading.value = true;
    try {
      final response = await _authApiService.signin(
        email: email,
        password: password,
      );

      if (response.success && response.data != null) {
        // Handle successful login
        user.value = User.fromJson(response.data!);
        Get.offAllNamed('/home');
      } else {
        // Handle login error
        Get.snackbar('Error', response.error?.message ?? 'Login failed');
      }
    } catch (e) {
      Get.snackbar('Error', 'An unexpected error occurred');
    } finally {
      loading.value = false;
    }
  }
}
```

## Migration Guide

To migrate existing code to use the new API architecture:

1. **Replace direct Dio calls** with the appropriate API service methods
2. **Update error handling** to use the new exception types
3. **Use ApiResponse wrappers** instead of raw Response objects
4. **Update bindings** to include the new API services
5. **Test thoroughly** with both success and error scenarios
