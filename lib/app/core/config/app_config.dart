import 'package:flutter/foundation.dart';
import 'package:somayya_academy/app/core/constants/api_constants.dart';

enum AppFlavor { dev, prod }

class AppConfig {
  static AppFlavor? _flavor;

  @visibleForTesting
  static void setTestFlavor(AppFlavor flavor) {
    _flavor = flavor;
  }

  @visibleForTesting
  static void resetFlavor() {
    _flavor = null;
  }

  static AppFlavor get flavor {
    if (_flavor != null) return _flavor!;

    // Get flavor from Flutter's appFlavor
    const flavorString = String.fromEnvironment('FLUTTER_APP_FLAVOR');
    if (flavorString.isNotEmpty) {
      _flavor = AppFlavor.values.firstWhere(
        (f) => f.name == flavorString,
        orElse: () => AppFlavor.prod,
      );
    } else {
      // Fallback: try to detect from package name or other means
      _flavor = _detectFlavorFromContext();
    }

    return _flavor!;
  }

  static AppFlavor _detectFlavorFromContext() {
    // This is a fallback method when FLUTTER_APP_FLAVOR is not available
    // You can implement additional detection logic here if needed
    return kDebugMode ? AppFlavor.dev : AppFlavor.prod;
  }

  static bool get isDev => flavor == AppFlavor.dev;

  static bool get isProd => flavor == AppFlavor.prod;

  // API Configuration
  static String get apiBaseUrl {
    switch (flavor) {
      case AppFlavor.dev:
        return ApiConstants.devBaseUrl;
      case AppFlavor.prod:
        return ApiConstants.prodBaseUrl;
    }
  }

  // App Name
  static String get appName {
    switch (flavor) {
      case AppFlavor.dev:
        return 'Somayya Academy Dev';
      case AppFlavor.prod:
        return 'Somayya Academy';
    }
  }

  // Database Configuration
  static String get databaseName {
    switch (flavor) {
      case AppFlavor.dev:
        return 'somayya_academy_dev.db';
      case AppFlavor.prod:
        return 'somayya_academy.db';
    }
  }

  // Feature Flags
  static bool get enableDebugFeatures {
    switch (flavor) {
      case AppFlavor.dev:
        return true;
      case AppFlavor.prod:
        return false;
    }
  }

  // Logging Configuration
  static bool get enableDetailedLogging {
    switch (flavor) {
      case AppFlavor.dev:
        return true;
      case AppFlavor.prod:
        return false;
    }
  }

  // Analytics Configuration
  static bool get enableAnalytics {
    switch (flavor) {
      case AppFlavor.dev:
        return false; // Disable analytics in dev
      case AppFlavor.prod:
        return true;
    }
  }

  // Crash Reporting Configuration
  static bool get enableCrashReporting {
    switch (flavor) {
      case AppFlavor.dev:
        return false; // Disable crash reporting in dev
      case AppFlavor.prod:
        return true;
    }
  }

  // Print current configuration (useful for debugging)
  static void printConfig() {
    if (kDebugMode) {
      print('=== App Configuration ===');
      print('Flavor: ${flavor.name}');
      print('App Name: $appName');
      print('API Base URL: $apiBaseUrl');
      print('Database Name: $databaseName');
      print('Debug Features: $enableDebugFeatures');
      print('Detailed Logging: $enableDetailedLogging');
      print('Analytics: $enableAnalytics');
      print('Crash Reporting: $enableCrashReporting');
      print('========================');
    }
  }
}
