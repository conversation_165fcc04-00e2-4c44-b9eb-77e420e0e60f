import 'dart:convert';

import 'package:cryptography/cryptography.dart';

/// Encrypts a plaintext string using AES-GCM with a fixed key derived from a password.
class PasswordEncryptor {
  /// Encrypts a plaintext string using AES-GCM with a fixed key derived from a password.
  Future<String> encryptPassword(
    String plainText, {
    String keyStr = "somayyaacademy",
  }) async {
    // Select the AES-GCM algorithm with a 128-bit key.
    final algorithm = AesGcm.with128bits();

    // Hash the key string using SHA-256 to create a secure key.
    // This corresponds to `crypto.subtle.digest("SHA-256", ...)`.
    final hashAlgorithm = Sha256();
    final keyHash = await hashAlgorithm.hash(utf8.encode(keyStr));

    // Use the first 16 bytes (128 bits) of the hash as the AES key.
    final secretKey = SecretKey(keyHash.bytes.sublist(0, 16));

    // Generate a random 12-byte (96-bit) nonce (IV).
    final nonce = algorithm.newNonce();

    // Convert the plaintext string to bytes for encryption.
    final plainTextBytes = utf8.encode(plainText);

    // Encrypt the data.
    // The `encrypt` method returns a `SecretBox` object, which contains
    // the ciphertext, nonce, and a message authentication code (MAC).
    final secretBox = await algorithm.encrypt(
      plainTextBytes,
      secretKey: secretKey,
      nonce: nonce,
    );

    // Encode the nonce (IV) and the combined ciphertext + MAC to Base64.
    final ivBase64 = base64Encode(secretBox.nonce);
    final combinedCiphertext = secretBox.cipherText + secretBox.mac.bytes;
    final cipherBase64 = base64Encode(combinedCiphertext);

    // Return in the same "iv:ciphertext" format.
    return '$ivBase64:$cipherBase64';
  }
}
