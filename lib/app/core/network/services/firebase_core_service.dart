import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

import '../../config/app_config.dart';
import '../../firebase/firebase_options.dart' as firebase_options_prod;
import '../../firebase/firebase_options_dev.dart' as firebase_options_dev;

/// Service class to handle Firebase initialization based on app flavors
class FirebaseService {
  static bool _initialized = false;

  /// Initialize Firebase with the appropriate configuration based on the current flavor
  static Future<void> initialize() async {
    if (_initialized) {
      if (kDebugMode) {
        print('Firebase already initialized');
      }
      return;
    }

    try {
      // Print configuration for debugging
      if (AppConfig.enableDetailedLogging) {
        // FirebaseConfig.printConfig();
      }

      // Initialize Firebase based on flavor
      await _initializeForFlavor(AppConfig.flavor);

      _initialized = true;

      if (kDebugMode) {
        print(
          '✅ Firebase initialized successfully for ${AppConfig.flavor.name} flavor',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Firebase initialization failed: $e');
      }
      rethrow;
    }
  }

  /// Initialize Firebase for a specific flavor
  static Future<void> _initializeForFlavor(AppFlavor flavor) async {
    switch (flavor) {
      case AppFlavor.dev:
        await Firebase.initializeApp(
          options: firebase_options_dev.DefaultFirebaseOptions.currentPlatform,
        );
        break;
      case AppFlavor.prod:
        await Firebase.initializeApp(
          options: firebase_options_prod.DefaultFirebaseOptions.currentPlatform,
        );
        break;
    }
  }

  /// Check if Firebase has been initialized
  static bool get isInitialized => _initialized;

  /// Reset the initialization status for testing purposes
  @visibleForTesting
  static void reset() {
    _initialized = false;
  }
}
