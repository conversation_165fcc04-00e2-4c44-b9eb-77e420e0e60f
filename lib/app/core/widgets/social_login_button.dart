// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:somayya_academy/app/core/constants/string_constants.dart';

// class SocialLoginButton extends StatelessWidget {
//   final VoidCallback? onPressed;
//   final SocialLoginType loginType;
//   const SocialLoginButton({super.key, this.onPressed, required this.loginType});

//   @override
//   Widget build(BuildContext context) {
//     return ElevatedButton(
//       onPressed: onPressed,
//       style: ElevatedButton.styleFrom(
//         backgroundColor: Colors.white,
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
//         padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
//       ),
//       child: SvgPicture.asset(_getSocialIcon(loginType), width: 30),
//     );
//   }

//   String _getSocialIcon(SocialLoginType loginType) {
//     switch (loginType) {
//       case SocialLoginType.google:
//         return StringConstants.googleIcon;
//       case SocialLoginType.facebook:
//         return StringConstants.facebookIcon;
//       case SocialLoginType.microsoft:
//         return StringConstants.microsoftIcon;
//       case SocialLoginType.apple:
//         return StringConstants.appleIcon;
//       // default:
//       //   return StringConstants.facebookIcon;
//     }
//   }
// }

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:somayya_academy/app/core/constants/string_constants.dart';

enum SocialLoginType { google, facebook, apple, microsoft }

class SocialLoginButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final SocialLoginType loginType;

  const SocialLoginButton({
    super.key,
    required this.onPressed,
    required this.loginType,
  });

  @override
  Widget build(BuildContext context) {
    final iconAsset = _getIconAsset(loginType);
    final labelText = _getLabelText(loginType);
    final style = _getButtonStyle(loginType, context);

    return ElevatedButton(
      onPressed: onPressed,
      style: style,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            iconAsset,
            width: 24,
            colorFilter:
                (loginType == SocialLoginType.facebook ||
                    loginType == SocialLoginType.apple)
                ? ColorFilter.mode(
                    style.foregroundColor?.resolve({}) ?? Colors.white,
                    BlendMode.srcIn,
                  )
                : null,
          ),
          const SizedBox(width: 12),
          Text(
            labelText,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  // Helper to get the icon asset path.
  String _getIconAsset(SocialLoginType type) {
    switch (type) {
      case SocialLoginType.google:
        return StringConstants.googleIcon;
      case SocialLoginType.facebook:
        return StringConstants.facebookIcon;
      case SocialLoginType.apple:
        return StringConstants.appleIcon;
      case SocialLoginType.microsoft:
        return StringConstants.microsoftIcon;
    }
  }

  // Helper to get the button label text.
  String _getLabelText(SocialLoginType type) {
    switch (type) {
      case SocialLoginType.google:
        return StringConstants.continueWithGoogle;
      case SocialLoginType.facebook:
        return StringConstants.continueWithFacebook;
      case SocialLoginType.apple:
        return StringConstants.continueWithApple;
      case SocialLoginType.microsoft:
        return StringConstants.continueWithMicrosoft;
    }
  }

  // Helper to get button style
  ButtonStyle _getButtonStyle(SocialLoginType type, BuildContext context) {
    final baseStyle = ElevatedButton.styleFrom(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      padding: const EdgeInsets.symmetric(vertical: 14),
      elevation: 1,
    );

    switch (type) {
      case SocialLoginType.google:
        return baseStyle.copyWith(
          backgroundColor: WidgetStatePropertyAll(Colors.white),
          foregroundColor: WidgetStatePropertyAll(Colors.black87),
          side: WidgetStatePropertyAll(
            BorderSide(color: Colors.grey.shade300, width: 1.5),
          ),
        );
      case SocialLoginType.facebook:
        return baseStyle.copyWith(
          backgroundColor: WidgetStatePropertyAll(const Color(0xFF1877F2)),
          foregroundColor: WidgetStatePropertyAll(Colors.white),
        );
      case SocialLoginType.apple:
        // Apple button color can depend on the theme (light/dark mode)
        final isDarkMode = Theme.of(context).brightness == Brightness.dark;
        return baseStyle.copyWith(
          backgroundColor: WidgetStatePropertyAll(
            isDarkMode ? Colors.white : Colors.black,
          ),
          foregroundColor: WidgetStatePropertyAll(
            isDarkMode ? Colors.black : Colors.white,
          ),
        );
      case SocialLoginType.microsoft:
        return baseStyle.copyWith(
          backgroundColor: WidgetStatePropertyAll(Colors.white),
          foregroundColor: WidgetStatePropertyAll(Colors.grey.shade800),
          side: WidgetStatePropertyAll(
            BorderSide(color: Colors.grey.shade400, width: 1),
          ),
        );
    }
  }
}
