import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AppDropdown<T> extends StatelessWidget {
  final String labelText;
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final void Function(T?)? onChanged;
  final String? Function(T?)? validator;

  const AppDropdown({
    super.key,
    required this.labelText,
    this.value,
    required this.items,
    this.onChanged,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Get.theme;
    // DropdownMenu requires a controller, so we create one here.
    // We can't easily use a validator with DropdownMenu without more complex state management,
    // so we'll wrap it in a FormField to handle validation.
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: FormField<T>(
        initialValue: value,
        validator: validator,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        builder: (state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DropdownMenu<T>(
                initialSelection: value,
                label: Text(labelText),
                expandedInsets: EdgeInsets.zero,
                dropdownMenuEntries: items.map((item) {
                  return DropdownMenuEntry<T>(
                    value: item.value as T,
                    label: (item.child as Text).data!,
                  );
                }).toList(),
                onSelected: (T? newValue) {
                  state.didChange(newValue);
                  if (onChanged != null) {
                    onChanged!(newValue);
                  }
                },
              ),
              if (state.hasError)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0, left: 12.0),
                  child: Text(
                    state.errorText!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
