class ApiConstants {
  // Base URLs
  static const String devBaseUrl =
      'http://13.234.22.118:8096/somayya-academy/api/v1';
  static const String prodBaseUrl =
      'http://13.234.22.118:8096/somayya-academy/api/v1';

  // API Timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);

  // Authentication Endpoints
  static const String authBase = '/auth';
  static const String validateOtp = '$authBase/validate-otp';
  static const String updatePassword = '$authBase/update-password';
  static const String signup = '$authBase/signup';
  static const String signin = '$authBase/signin';
  static const String setPassword = '$authBase/set-password';
  static const String resetPassword = '$authBase/reset-password';
  static const String emailVerification = '$authBase/email-verification';
  static const String signout = '$authBase/signout';
  static const String refreshToken = '$authBase/refresh-token';

  // Topic Controller Endpoints
  static const String topicsBase = '/topics';
  static const String addTopic = '$topicsBase/add';

  // Course Controller Endpoints
  static const String coursesBase = '/courses';
  static const String addCourse = '$coursesBase/add';
  static const String getAllCourses = '$coursesBase/getAll';

  // Dynamic endpoints (require path parameters)
  static String deleteCourse(String courseId) => '$coursesBase/$courseId';
  static String updateCourse(String courseId) => '$coursesBase/$courseId';

  // Common Headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // HTTP Status Codes
  static const int statusOk = 200;
  static const int statusCreated = 201;
  static const int statusBadRequest = 400;
  static const int statusUnauthorized = 401;
  static const int statusForbidden = 403;
  static const int statusNotFound = 404;
  static const int statusInternalServerError = 500;

  // Error Messages
  static const String networkError = 'Network connection error';
  static const String timeoutError = 'Request timeout';
  static const String unauthorizedError = 'Unauthorized access';
  static const String serverError = 'Server error occurred';
  static const String unknownError = 'An unknown error occurred';
}
