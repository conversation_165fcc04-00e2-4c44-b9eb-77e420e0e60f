import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// AppTheme centralizes all theming for the application.
/// Elements are grouped by purpose for clarity and maintainability.
class AppTheme {
  // -------------------- COLORS --------------------
  static const Color primaryColor = Colors.deepPurple;
  static const Color secondaryColor = Colors.amber;
  static const Color errorColor = Colors.red;
  static const Color lightBackground = Color(0xFFF5F5F5);
  static const Color darkBackground = Color(0xFF212121);
  static const Color lightCard = Colors.white;
  static const Color darkCard = Color(0xFF2C2C2C);

  // -------------------- BORDERS --------------------
  static const double borderRadius = 12.0;
  static const BorderSide greyBorder = BorderSide(color: Colors.grey);
  static const BorderSide blueBorder = BorderSide(
    color: primaryColor,
    width: 2,
  );
  static const BorderSide redBorder = BorderSide(color: errorColor);

  // -------------------- TYPOGRAPHY --------------------
  static TextTheme getLightTextTheme() => GoogleFonts.robotoTextTheme(
    const TextTheme(
      displayLarge: TextStyle(
        fontSize: 72,
        fontWeight: FontWeight.bold,
        color: Colors.black,
      ),
      displayMedium: TextStyle(
        fontSize: 36,
        fontStyle: FontStyle.italic,
        color: Colors.black87,
      ),
      displaySmall: TextStyle(fontSize: 14, color: Colors.black87),
      headlineMedium: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: Colors.black,
      ),
      bodyLarge: TextStyle(fontSize: 16, color: Colors.black87),
      bodyMedium: TextStyle(fontSize: 14, color: Colors.black87),
      bodySmall: TextStyle(fontSize: 12, color: Colors.black87),
    ),
  );

  static TextTheme getDarkTextTheme() => GoogleFonts.robotoTextTheme(
    const TextTheme(
      displayLarge: TextStyle(
        fontSize: 72,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
      displayMedium: TextStyle(
        fontSize: 36,
        fontStyle: FontStyle.italic,
        color: Colors.white70,
      ),
      displaySmall: TextStyle(fontSize: 14, color: Colors.white70),
      headlineMedium: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: Colors.white,
      ),
      bodyLarge: TextStyle(fontSize: 16, color: Colors.white70),
      bodyMedium: TextStyle(fontSize: 14, color: Colors.white70),
      bodySmall: TextStyle(fontSize: 12, color: Colors.white70),
    ),
  );

  // -------------------- INPUT DECORATION --------------------
  static InputDecorationTheme inputDecorationTheme(Brightness brightness) {
    return InputDecorationTheme(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: greyBorder,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: blueBorder,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: greyBorder,
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: redBorder,
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: BorderSide(color: errorColor, width: 2),
      ),
      labelStyle: GoogleFonts.roboto(color: Colors.grey),
      hintStyle: GoogleFonts.roboto(color: Colors.grey),
    );
  }

  // -------------------- BUTTON THEMES --------------------
  static ElevatedButtonThemeData elevatedButtonTheme(Brightness brightness) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 5,
        foregroundColor: Colors.white,
        backgroundColor: brightness == Brightness.light
            ? Colors.white
            : Colors.black12,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
          side: BorderSide(color: primaryColor),
        ),
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        textStyle: GoogleFonts.roboto(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  static TextButtonThemeData textButtonTheme() {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      ),
    );
  }

  static OutlinedButtonThemeData outlinedButtonTheme() {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: BorderSide(color: primaryColor, width: 1.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      ),
    );
  }

  // -------------------- CARD & DIALOG THEMES --------------------
  static CardThemeData cardTheme(Color cardColor) => CardThemeData(
    elevation: 2,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(borderRadius),
    ),
    color: cardColor,
  );

  static DialogThemeData dialogTheme(Brightness brightness, Color cardColor) =>
      DialogThemeData(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        backgroundColor: cardColor,
        titleTextStyle: GoogleFonts.roboto(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: brightness == Brightness.light ? Colors.black87 : Colors.white,
        ),
        contentTextStyle: GoogleFonts.roboto(
          fontSize: 16,
          color: brightness == Brightness.light
              ? Colors.black54
              : Colors.white70,
        ),
      );

  // -------------------- OTHER WIDGET THEMES --------------------
  static AppBarTheme appBarTheme(Brightness brightness) => AppBarTheme(
    backgroundColor: brightness == Brightness.light
        ? primaryColor
        : Colors.black,
    foregroundColor: Colors.white,
    elevation: 1,
    titleTextStyle: GoogleFonts.roboto(
      fontSize: 20,
      fontWeight: FontWeight.bold,
    ),
    centerTitle: true,
  );

  static FloatingActionButtonThemeData fabTheme() =>
      const FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        shape: CircleBorder(),
      );

  static DividerThemeData dividerTheme() =>
      const DividerThemeData(color: Colors.grey, thickness: 1, space: 1);

  static IconThemeData iconTheme() =>
      const IconThemeData(color: primaryColor, size: 24);

  static ChipThemeData chipTheme(Brightness brightness) => ChipThemeData(
    backgroundColor: brightness == Brightness.light
        ? Colors.grey[200]
        : Colors.grey[800],
    selectedColor: Colors.blue[brightness == Brightness.light ? 100 : 800],
    secondarySelectedColor: primaryColor,
    labelStyle: GoogleFonts.roboto(
      color: brightness == Brightness.light ? Colors.black87 : Colors.white70,
    ),
    secondaryLabelStyle: GoogleFonts.roboto(color: Colors.white),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(borderRadius),
    ),
  );

  static SnackBarThemeData snackBarTheme(Brightness brightness) =>
      SnackBarThemeData(
        backgroundColor:
            Colors.blue[brightness == Brightness.light ? 800 : 900],
        contentTextStyle: GoogleFonts.roboto(color: Colors.white),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        behavior: SnackBarBehavior.floating,
      );

  static BottomNavigationBarThemeData bottomNavTheme(Brightness brightness) =>
      BottomNavigationBarThemeData(
        backgroundColor: brightness == Brightness.light
            ? Colors.white
            : Colors.grey,
        selectedItemColor: primaryColor,
        unselectedItemColor: brightness == Brightness.light
            ? Colors.grey
            : Colors.white70,
        selectedLabelStyle: GoogleFonts.roboto(fontWeight: FontWeight.w600),
      );

  // -------------------- BASE THEME FACTORY --------------------
  static ThemeData _baseTheme(
    Brightness brightness,
    Color background,
    Color cardColor,
  ) {
    final isLight = brightness == Brightness.light;
    return ThemeData(
      useMaterial3: true,
      primaryColor: primaryColor,
      colorScheme: ColorScheme.fromSwatch(
        primarySwatch: Colors.deepPurple,
        brightness: brightness,
        backgroundColor: background,
        errorColor: errorColor,
      ).copyWith(secondary: secondaryColor),
      scaffoldBackgroundColor: background,
      visualDensity: VisualDensity.adaptivePlatformDensity,
      textTheme: isLight ? getLightTextTheme() : getDarkTextTheme(),
      appBarTheme: appBarTheme(brightness),
      inputDecorationTheme: inputDecorationTheme(brightness),
      elevatedButtonTheme: elevatedButtonTheme(brightness),
      textButtonTheme: textButtonTheme(),
      outlinedButtonTheme: outlinedButtonTheme(),
      cardTheme: cardTheme(cardColor),
      dialogTheme: dialogTheme(brightness, cardColor),
      floatingActionButtonTheme: fabTheme(),
      dividerTheme: dividerTheme(),
      iconTheme: iconTheme(),
      chipTheme: chipTheme(brightness),
      snackBarTheme: snackBarTheme(brightness),
      bottomNavigationBarTheme: bottomNavTheme(brightness),
    );
  }

  /// Light theme for the app.
  static ThemeData get light =>
      _baseTheme(Brightness.light, lightBackground, lightCard);

  /// Dark theme for the app.
  static ThemeData get dark =>
      _baseTheme(Brightness.dark, darkBackground, darkCard);
}
