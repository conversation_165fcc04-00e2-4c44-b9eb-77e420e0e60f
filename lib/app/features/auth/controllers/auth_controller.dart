import 'package:get/get.dart';

import '../../../core/utils/password_encryptor.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/social_auth_service.dart';

class AuthController extends GetxController {
  final AuthService _authApiService = Get.find();
  final SocialAuthService _socialAuthService = Get.find();

  Rxn<User> user = Rxn<User>();
  RxBool loading = false.obs;
  RxBool googleLoading = false.obs;
  RxBool facebookLoading = false.obs;
  RxBool microsoftLoading = false.obs;

  @override
  void onInit() async {
    super.onInit();
    // Check if user is authenticated with our backend
    await _checkCurrentUser();
  }

  Future<void> _checkCurrentUser() async {
    final isAuthenticated = await _authApiService.isAuthenticated();
    if (isAuthenticated) {
      // If you have an endpoint to get user profile, call it here.
      // For now, we'll create a placeholder user if authenticated.
      final token = await _authApiService.getCurrentToken();
      // This part might need to be adjusted based on whether you can fetch
      // user details with just a token.
      user.value = User(id: '', name: 'User', email: '', token: token ?? '');
    }
  }

  Future<void> signInWithGoogle() async {
    googleLoading.value = true;
    try {
      final response = await _socialAuthService.signInWithGoogle();
      if (response != null && response.success && response.data != null) {
        _updateUserFromApiResponse(response.data!);
      }
    } finally {
      googleLoading.value = false;
    }
  }

  Future<void> signInWithFacebook() async {
    facebookLoading.value = true;
    try {
      final response = await _socialAuthService.signInWithFacebook();
      if (response != null && response.success && response.data != null) {
        _updateUserFromApiResponse(response.data!);
      }
    } finally {
      facebookLoading.value = false;
    }
  }

  Future<void> signInWithMicrosoft() async {
    microsoftLoading.value = true;
    try {
      final response = await _socialAuthService.signInWithMicrosoft();
      if (response != null && response.success && response.data != null) {
        _updateUserFromApiResponse(response.data!);
      }
    } finally {
      microsoftLoading.value = false;
    }
  }

  Future<void> login(String email, String password) async {
    loading.value = true;
    try {
      final encryptedPassword = await PasswordEncryptor().encryptPassword(
        password,
      );
      final response = await _authApiService.signin(
        email: email,
        password: encryptedPassword,
      );
      if (response.success && response.data != null) {
        _updateUserFromApiResponse(response.data!, email: email);
      }
    } finally {
      loading.value = false;
    }
  }

  Future<void> requestOtp(String email) async {
    loading.value = true;
    try {
      await _authApiService.sendEmailVerification(email: email);
    } finally {
      loading.value = false;
    }
  }

  Future<bool> verifyOtp(String email, String otp) async {
    loading.value = true;
    try {
      final res = await _authApiService.validateOtp(email: email, otp: otp);
      return res.success;
    } finally {
      loading.value = false;
    }
  }

  Future<void> registerWithPassword(
    String email,
    String otp,
    String password,
  ) async {
    loading.value = true;
    try {
      final res = await _authApiService.setPassword(
        email: email,
        otp: otp,
        password: password,
      );
      if (res.success && res.data != null) {
        _updateUserFromApiResponse(res.data!, email: email);
      }
    } finally {
      loading.value = false;
    }
  }

  Future<User> register(String name, String email, String password) async {
    loading.value = true;
    try {
      final res = await _authApiService.signup(
        name: name,
        email: email,
        password: password,
      );
      if (res.success && res.data != null) {
        final newUser = _updateUserFromApiResponse(res.data!, email: email);
        return newUser!;
      }
      throw Exception('Registration failed');
    } finally {
      loading.value = false;
    }
  }

  Future<void> updateProfile({
    required String name,
    required String university,
    required String semester,
    required String branch,
  }) async {
    loading.value = true;
    try {
      // Assuming update profile API is available in authApiService (add it if needed)
      // Placeholder implementation for now:
      final updatedUser = User(
        id: user.value?.id ?? '',
        name: name,
        email: user.value?.email ?? '',
        token: user.value?.token ?? '',
      );
      user.value = updatedUser;
    } finally {
      loading.value = false;
    }
  }

  Future<void> logout() async {
    await _socialAuthService.logout();
    await _authApiService.signout();
    user.value = null;
  }

  User? _updateUserFromApiResponse(Map<String, dynamic> data, {String? email}) {
    final newUser = User(
      id: data['id'] ?? '',
      name: data['name'] ?? '',
      email: data['email'] ?? email ?? '',
      token: data['token'] ?? '',
    );
    user.value = newUser;
    return newUser;
  }
}
