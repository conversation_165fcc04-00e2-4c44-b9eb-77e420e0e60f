import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart' as firebase;
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';

import '../../../core/models/api_response.dart';
import 'auth_service.dart';

class SocialAuthService extends GetxService {
  final _firebaseAuth = firebase.FirebaseAuth.instance;
  final _googleSignIn = GoogleSignIn.instance;
  final _facebookSignIn = FacebookAuth.instance;
  final _authService = Get.find<AuthService>();

  /// Sign in with Google
  Future<ApiResponse<Map<String, dynamic>>?> signInWithGoogle() async {
    try {
      final googleSignInAccount = await _googleSignIn.authenticate();

      final googleAuth = googleSignInAccount.authentication;
      final credential = firebase.GoogleAuthProvider.credential(
        idToken: googleAuth.idToken,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(
        credential,
      );
      final user = userCredential.user?.providerData.first;

      if (user == null) {
        throw Exception('Firebase user not found after Google sign-in.');
      }

      return await _signInFromSocial(
        userEmail: user.email ?? '',
        providerName: 'google',
        socialUserId: user.uid ?? '',
      );
    } catch (e) {
      log('Error during Google sign-in: $e');
      rethrow;
    }
  }

  /// Sign in with Facebook
  Future<ApiResponse<Map<String, dynamic>>?> signInWithFacebook() async {
    try {
      final result = await _facebookSignIn.login();
      if (result.status == LoginStatus.success) {
        final fbCredential = firebase.FacebookAuthProvider.credential(
          result.accessToken?.tokenString ?? '',
        );
        final userCredential = await _firebaseAuth.signInWithCredential(
          fbCredential,
        );
        final user = userCredential.user?.providerData.first;

        if (user == null) {
          throw Exception('Firebase user not found after Facebook sign-in.');
        }

        return await _signInFromSocial(
          userEmail: user.email ?? '',
          providerName: 'facebook',
          socialUserId: user.uid ?? '',
        );
      }
      return null;
    } catch (e) {
      log('Error during Facebook sign-in: $e');
      rethrow;
    }
  }

  /// Sign in with Microsoft
  Future<ApiResponse<Map<String, dynamic>>?> signInWithMicrosoft() async {
    try {
      final microsoftProvider = firebase.MicrosoftAuthProvider();
      final userCredential = await _firebaseAuth.signInWithProvider(
        microsoftProvider,
      );
      final user = userCredential.user?.providerData.first;

      if (user == null) {
        throw Exception('Firebase user not found after Microsoft sign-in.');
      }

      return await _signInFromSocial(
        userEmail: user.email ?? '',
        providerName: 'microsoft',
        socialUserId: user.uid ?? '',
      );
    } catch (e) {
      log('Error during Microsoft sign-in: $e');
      rethrow;
    }
  }

  Future<ApiResponse<Map<String, dynamic>>?> _signInFromSocial({
    required String userEmail,
    required String providerName,
    required String socialUserId,
  }) async {
    try {
      return await _authService.socialSignin(
        email: userEmail,
        providerName: providerName,
        socialUserId: socialUserId,
      );
    } catch (e) {
      log('Error during social sign-in: $e');
      rethrow;
    }
  }

  Future<void> logout() async {
    await _googleSignIn.signOut();
    await _facebookSignIn.logOut();
    await _firebaseAuth.signOut();
    await _authService.clearAuthData();
  }
}
