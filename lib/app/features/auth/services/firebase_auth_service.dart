import 'package:firebase_auth/firebase_auth.dart' as firebase;
import 'package:get/get.dart';

import '../models/user_model.dart';

class FirebaseAuthService extends GetxService {
  final firebase.FirebaseAuth _firebaseAuth = firebase.FirebaseAuth.instance;

  /// Emits current Firebase user (converted to app's User model)
  Stream<User?> get user =>
      _firebaseAuth.authStateChanges().map(_userFromFirebase);

  /// Convert Firebase user to your app's User model
  User? _userFromFirebase(firebase.User? user) {
    if (user == null) return null;

    return User(
      id: user.uid,
      email: user.email ?? '',
      name: user.displayName ?? '',
      token: '', // Token is managed separately
    );
  }

  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }
}
