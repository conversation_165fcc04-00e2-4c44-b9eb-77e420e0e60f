import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:somayya_academy/app/core/widgets/app_button.dart';
import 'package:somayya_academy/app/core/widgets/app_dropdown.dart';
import 'package:somayya_academy/app/core/widgets/app_text_field.dart';

import '../controllers/auth_controller.dart';

class RegisterProfileView extends StatefulWidget {
  const RegisterProfileView({super.key});

  @override
  State<RegisterProfileView> createState() => _RegisterProfileViewState();
}

class _RegisterProfileViewState extends State<RegisterProfileView> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _nameController = TextEditingController();

  final List<String> _universities = ['University A', 'University B'];
  final List<String> _branches = ['CSE', 'ECE'];
  final List<String> _semesters = List.generate(8, (i) => 'Semester ${i + 1}');

  String? _selectedUniversity;
  String? _selectedBranch;
  String? _selectedSemester;

  String? _required(String? v) => v == null || v.isEmpty ? 'Required' : null;

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    return Scaffold(
      appBar: AppBar(title: const Text('Register - Profile')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: SingleChildScrollView(
            child: Column(
              children: [
                AppTextField(
                  controller: _nameController,
                  labelText: 'Full Name',
                  validator: _required,
                ),
                AppDropdown<String>(
                  labelText: 'University',
                  items: _universities
                      .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                      .toList(),
                  value: _selectedUniversity,
                  onChanged: (v) => setState(() => _selectedUniversity = v),
                  validator: _required,
                ),
                AppDropdown<String>(
                  labelText: 'Semester',
                  items: _semesters
                      .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                      .toList(),
                  value: _selectedSemester,
                  onChanged: (v) => setState(() => _selectedSemester = v),
                  validator: _required,
                ),
                AppDropdown<String>(
                  labelText: 'Branch',
                  items: _branches
                      .map((e) => DropdownMenuItem(value: e, child: Text(e)))
                      .toList(),
                  value: _selectedBranch,
                  onChanged: (v) => setState(() => _selectedBranch = v),
                  validator: _required,
                ),
                const SizedBox(height: 16),
                Obx(
                  () => AppButton(
                    text: 'Finish',
                    isLoading: authController.loading.value,
                    onPressed: () async {
                      if (_formKey.currentState!.validate()) {
                        await authController.updateProfile(
                          name: _nameController.text,
                          university: _selectedUniversity!,
                          semester: _selectedSemester!,
                          branch: _selectedBranch!,
                        );
                        if (context.mounted) {
                          _nameController.clear();
                          setState(() {
                            _selectedUniversity = null;
                            _selectedSemester = null;
                            _selectedBranch = null;
                          });
                          _formKey.currentState!.reset();
                          context.go('/login');
                        }
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
