import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:somayya_academy/app/core/widgets/app_button.dart';

import '../controllers/auth_controller.dart';
import '../widgets/otp_input.dart';

class RegisterVerifyOtpView extends StatefulWidget {
  const RegisterVerifyOtpView({super.key, required this.email});

  final String email;

  @override
  State<RegisterVerifyOtpView> createState() => _RegisterVerifyOtpViewState();
}

class _RegisterVerifyOtpViewState extends State<RegisterVerifyOtpView> {
  String? _otp;
  final _otpController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    return Scaffold(
      appBar: AppBar(title: const Text('Register - Verify OTP')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text('Enter OTP sent to ${widget.email}'),
            const SizedBox(height: 16),
            OtpInput(
              length: 6,
              controller: _otpController,
              onCompleted: (otp) {
                setState(() {
                  _otp = otp;
                });
              },
            ),
            const SizedBox(height: 24),
            Obx(
              () => AppButton(
                text: 'Verify OTP',
                isLoading: authController.loading.value,
                onPressed: _otp == null || _otp!.length != 6
                    ? null
                    : () async {
                        final success = await authController.verifyOtp(
                          widget.email,
                          _otp!,
                        );
                        if (success && context.mounted) {
                          _otpController.clear();
                          context.push(
                            '/register/password',
                            extra: {'email': widget.email, 'otp': _otp!},
                          );
                        } else if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Invalid OTP. Please try again.'),
                            ),
                          );
                        }
                      },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
