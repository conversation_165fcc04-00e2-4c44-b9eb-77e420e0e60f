import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:somayya_academy/app/core/widgets/app_button.dart';
import 'package:somayya_academy/app/core/widgets/app_text_field.dart';
import 'package:somayya_academy/app/features/auth/controllers/auth_controller.dart';

import '../widgets/password_strength_indicator.dart';

class RegisterPasswordView extends StatefulWidget {
  const RegisterPasswordView({
    super.key,
    required this.email,
    required this.otp,
  });

  final String email;
  final String otp;

  @override
  State<RegisterPasswordView> createState() => _RegisterPasswordViewState();
}

class _RegisterPasswordViewState extends State<RegisterPasswordView> {
  final _formKey = GlobalKey<FormState>();
  final _pwd1 = TextEditingController();
  final _pwd2 = TextEditingController();

  @override
  void initState() {
    super.initState();
    _pwd1.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _pwd1.dispose();
    _pwd2.dispose();
    super.dispose();
  }

  String? _passwordValidator(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (!value.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain an uppercase letter.';
    }
    if (!value.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain a lowercase letter.';
    }
    if (!value.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain a number.';
    }
    if (!value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return 'Password must contain a special character.';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    return Scaffold(
      appBar: AppBar(title: const Text('Register - Set Password')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            children: [
              AppTextField(
                controller: _pwd1,
                labelText: 'Password',
                obscureText: true,
                validator: _passwordValidator,
              ),
              const SizedBox(height: 8),
              PasswordStrengthIndicator(password: _pwd1.text),
              const SizedBox(height: 8),
              AppTextField(
                controller: _pwd2,
                labelText: 'Confirm Password',
                validator: (v) {
                  final err = _passwordValidator(v);
                  if (err != null) return err;
                  if (v != _pwd1.text) return 'Passwords do not match';
                  return null;
                },
              ),
              const SizedBox(height: 16),
              Obx(
                () => AppButton(
                  text: 'Next',
                  isLoading: authController.loading.value,
                  onPressed: () async {
                    if (_formKey.currentState!.validate()) {
                      await authController.registerWithPassword(
                        widget.email,
                        widget.otp,
                        _pwd1.text,
                      );
                      if (context.mounted) {
                        _pwd1.clear();
                        _pwd2.clear();
                        _formKey.currentState!.reset();
                        context.push('/register/profile');
                      }
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
