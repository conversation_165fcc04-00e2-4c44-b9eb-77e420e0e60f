import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:somayya_academy/app/core/constants/app_constants.dart';
import 'package:somayya_academy/app/core/constants/route_constants.dart';
import 'package:somayya_academy/app/core/constants/string_constants.dart';
import 'package:somayya_academy/app/core/utils/validator.dart';
import 'package:somayya_academy/app/core/widgets/app_button.dart';
import 'package:somayya_academy/app/core/widgets/app_text_field.dart';
import 'package:somayya_academy/app/core/widgets/social_login_button.dart';

import '../controllers/auth_controller.dart';

enum LoginType { email, google, facebook, microsoft }

class LoginView extends StatelessWidget {
  LoginView({super.key});

  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    return Scaffold(
      appBar: AppBar(title: const Text(StringConstants.login)),
      body: Padding(
        padding: const EdgeInsets.all(AppConstants.padding),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            children: [
              AppTextField(
                controller: _emailController,
                labelText: StringConstants.email,
                keyboardType: TextInputType.emailAddress,
                validator: Validator.validateEmail,
              ),
              AppTextField(
                controller: _passwordController,
                labelText: StringConstants.password,
                obscureText: true,
                validator: Validator.validatePassword,
              ),
              const SizedBox(height: AppConstants.sizedBoxHeight),
              Obx(
                () => AppButton(
                  text: StringConstants.login,
                  isLoading: authController.loading.value,
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      authController.login(
                        _emailController.text,
                        _passwordController.text,
                      );
                    }
                    if (authController.user.value != null) {
                      context.go(RouteConstants.home);
                    }
                  },
                ),
              ),
              const SizedBox(height: AppConstants.sizedBoxHeight),
              const Row(
                children: [
                  Expanded(child: Divider()),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text('OR'),
                  ),
                  Expanded(child: Divider()),
                ],
              ),
              const SizedBox(height: AppConstants.sizedBoxHeight),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: 20,
                children: [
                  SocialLoginButton(
                    loginType: SocialLoginType.google,
                    onPressed: authController.signInWithGoogle,
                  ),
                  SocialLoginButton(
                    loginType: SocialLoginType.facebook,
                    onPressed: authController.signInWithFacebook,
                  ),
                  SocialLoginButton(
                    loginType: SocialLoginType.microsoft,
                    onPressed: authController.signInWithMicrosoft,
                  ),
                  // SocialLoginButton(
                  //   onPressed: null,
                  //   loginType: SocialLoginType.apple,
                  // ),
                ],
              ),

              // TextButton(
              //   onPressed: () {
              //     context.push(RouteConstants.register);
              //   },
              //   child: const Text(StringConstants.noAccount),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
