import 'package:flutter/material.dart';

class PasswordStrengthIndicator extends StatelessWidget {
  final String password;

  const PasswordStrengthIndicator({super.key, required this.password});

  bool _hasUppercase(String value) => value.contains(RegExp(r'[A-Z]'));
  bool _hasLowercase(String value) => value.contains(RegExp(r'[a-z]'));
  bool _hasNumber(String value) => value.contains(RegExp(r'[0-9]'));
  bool _hasSpecialCharacter(String value) =>
      value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
  bool _is8Characters(String value) => value.length >= 8;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildValidationRow('At least 8 characters', _is8Characters(password)),
        _buildValidationRow(
          'At least 1 uppercase letter',
          _hasUppercase(password),
        ),
        _buildValidationRow(
          'At least 1 lowercase letter',
          _hasLowercase(password),
        ),
        _buildValidationRow('At least 1 number', _hasNumber(password)),
        _buildValidationRow(
          'At least 1 special character',
          _hasSpecialCharacter(password),
        ),
      ],
    );
  }

  Widget _buildValidationRow(String text, bool isValid) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          Icon(
            isValid ? Icons.check_circle : Icons.remove_circle_outline,
            color: isValid ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              color: isValid ? Colors.green : Colors.red,
              decoration: isValid ? TextDecoration.lineThrough : null,
              decorationColor: Colors.green,
            ),
          ),
        ],
      ),
    );
  }
}
