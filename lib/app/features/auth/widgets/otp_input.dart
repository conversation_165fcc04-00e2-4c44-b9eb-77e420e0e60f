import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class OtpInput extends StatelessWidget {
  const OtpInput({
    super.key,
    required this.length,
    required this.onCompleted,
    this.controller,
  });

  final int length;
  final void Function(String) onCompleted;
  final TextEditingController? controller;

  @override
  Widget build(BuildContext context) {
    final theme = Get.theme;
    return PinCodeTextField(
      appContext: context,
      length: length,
      autoFocus: true,
      onCompleted: onCompleted,
      controller: controller,
      animationType: AnimationType.fade,
      cursorColor: theme.primaryColor,
      cursorHeight: 20,
      pinTheme: PinTheme(
        shape: PinCodeFieldShape.box,
        borderRadius: BorderRadius.circular(8),
        fieldHeight: 40,
        fieldWidth: 40,
        activeColor: theme.primaryColor,
        activeFillColor: theme.primaryColor.withValues(alpha: .6),
        selectedColor: theme.primaryColor,
        inactiveColor: Colors.grey,
        errorBorderColor: Colors.red,
      ),
      onChanged: (_) {},
    );
  }
}
