import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../widgets/home_drawer.dart';

class HomeView extends StatelessWidget {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Get.theme;
    return Scaffold(
      appBar: AppBar(title: Text('Home')),
      body: Center(
        child: Text('Home View', style: theme.textTheme.headlineLarge),
      ),
      drawer: HomeDrawer.drawer(context),
    );
  }
}
