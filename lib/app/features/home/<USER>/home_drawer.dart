import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

class HomeDrawer {
  static Drawer drawer(BuildContext context) {
    return Drawer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                UserAccountsDrawerHeader(
                  decoration: BoxDecoration(color: Get.theme.primaryColor),
                  accountName: Text(
                    '<PERSON> Do<PERSON>',
                    style: Get.theme.textTheme.titleLarge?.copyWith(
                      color: Get.theme.colorScheme.onPrimary,
                    ),
                  ),
                  accountEmail: Text(
                    '<EMAIL>',
                    style: Get.theme.textTheme.bodyMedium?.copyWith(
                      color: Get.theme.colorScheme.onPrimary,
                    ),
                  ),
                  currentAccountPicture: const CircleAvatar(
                    backgroundImage: NetworkImage(
                      'https://cdn-icons-png.flaticon.com/512/149/149071.png',
                    ),
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.home),
                  title: const Text('Home'),
                  onTap: () {},
                ),
                ListTile(
                  leading: const Icon(Icons.settings),
                  title: const Text('Settings'),
                  onTap: () {},
                ),
                ListTile(
                  leading: const Icon(Icons.person),
                  title: const Text('Profile'),
                  onTap: () {},
                ),
              ],
            ),
          ),
          const Divider(),
          ListTile(
            leading: Icon(Icons.logout, color: Get.theme.colorScheme.error),
            title: Text(
              'Logout',
              style: TextStyle(color: Get.theme.colorScheme.error),
            ),
            dense: true,
            visualDensity: VisualDensity.compact,
            onTap: () => context.go('/login'),
          ),
          ListTile(
            dense: true,
            visualDensity: VisualDensity.compact,
            title: Text(
              'Version 1.0.0',
              textAlign: TextAlign.start,
              style: Get.theme.textTheme.bodySmall?.copyWith(fontSize: 10),
            ),
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }
}
