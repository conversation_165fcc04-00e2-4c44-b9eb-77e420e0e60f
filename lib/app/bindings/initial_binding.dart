import 'package:get/get.dart';
import 'package:somayya_academy/app/features/auth/services/firebase_auth_service.dart';
import 'package:somayya_academy/app/features/auth/services/social_auth_service.dart';

import '../core/network/services/api_service.dart';
import '../features/auth/services/auth_service.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Initialize base API service first
    Get.put<ApiService>(ApiService(), permanent: true);

    // Initialize feature-specific API services
    Get.put<AuthService>(AuthService(), permanent: true);
    Get.lazyPut(() => FirebaseAuthService());
    Get.lazyPut(() => SocialAuthService());
  }
}
