import 'package:go_router/go_router.dart';
import 'package:somayya_academy/app/core/constants/route_constants.dart';
import 'package:somayya_academy/app/features/home/<USER>/home_view.dart';

import '../features/auth/views/enter_email_view.dart';
import '../features/auth/views/login_view.dart';
import '../features/auth/views/register_email_view.dart';
import '../features/auth/views/register_password_view.dart';
import '../features/auth/views/register_profile_view.dart';
import '../features/auth/views/register_verify_otp_view.dart';
import '../features/auth/views/reset_password_view.dart';
import '../features/auth/views/verify_otp_view.dart';

class AppPages {
  static final router = GoRouter(
    routes: [
      GoRoute(
        path: RouteConstants.login,
        builder: (context, state) => LoginView(),
      ),
      GoRoute(
        path: RouteConstants.register,
        builder: (context, state) => const RegisterEmailView(),
      ),
      GoRoute(
        path: '/register/verify',
        builder: (context, state) {
          final email = state.extra as String;
          return RegisterVerifyOtpView(email: email);
        },
      ),
      GoRoute(
        path: '/register/password',
        builder: (context, state) {
          final map = state.extra as Map<String, String>;
          return RegisterPasswordView(email: map['email']!, otp: map['otp']!);
        },
      ),
      GoRoute(
        path: '/register/profile',
        builder: (context, state) => const RegisterProfileView(),
      ),
      GoRoute(
        path: RouteConstants.forgotPassword,
        builder: (context, state) =>
            const EnterEmailView(title: 'Forgot Password'),
      ),
      GoRoute(
        path: '/forgot-password/verify',
        builder: (context, state) {
          final email = state.extra as String;
          return VerifyOtpView(email: email);
        },
      ),
      GoRoute(
        path: '/forgot-password/reset',
        builder: (context, state) {
          final map = state.extra as Map<String, String>;
          return ResetPasswordView(email: map['email']!, otp: map['otp']!);
        },
      ),
      GoRoute(
        path: RouteConstants.changePassword,
        builder: (context, state) =>
            const EnterEmailView(title: 'Change Password'),
      ),
      GoRoute(
        path: RouteConstants.home,
        builder: (context, state) => const HomeView(),
      ),
    ],
    initialLocation: RouteConstants.login,
    // errorBuilder: (context, state) => ErrorPage(),
  );
}
