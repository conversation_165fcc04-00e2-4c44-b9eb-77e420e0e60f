name: so<PERSON><PERSON>_academy
description: "Somayya Academy - Educational Platform"
publish_to: "none"
version: 1.0.0+1

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  get: ^4.7.2
  go_router: ^16.0.0
  dio: ^5.8.0+1
  flutter_secure_storage: ^9.2.4
  pin_code_fields: ^8.0.1 # OTP input
  email_validator: ^3.0.0 # Email validation
  google_fonts: ^6.2.1
  firebase_core: ^3.15.2
  firebase_auth: ^5.7.0
  google_sign_in: ^7.1.1
  flutter_facebook_auth: ^7.1.2
  flutter_svg: ^2.2.0
  cryptography: ^2.7.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  meta: ^1.16.0
  http_mock_adapter: ^0.6.1
  mockito: ^5.5.0
  build_runner: ^2.6.0

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/social_icons/
