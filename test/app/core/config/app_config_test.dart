import 'package:flutter_test/flutter_test.dart';
import 'package:somayya_academy/app/core/config/app_config.dart';
import 'package:somayya_academy/app/core/constants/api_constants.dart';

void main() {
  group('AppConfig Tests', () {
    tearDown(() {
      // Reset the flavor after each test to ensure test isolation
      AppConfig.resetFlavor();
    });

    group('Dev Flavor', () {
      setUp(() {
        AppConfig.setTestFlavor(AppFlavor.dev);
      });

      test('isDev should be true and isProd should be false', () {
        expect(AppConfig.isDev, isTrue);
        expect(AppConfig.isProd, isFalse);
      });

      test('API base URL should be the dev URL', () {
        expect(AppConfig.apiBaseUrl, ApiConstants.devBaseUrl);
      });

      test('App name should be the dev name', () {
        expect(AppConfig.appName, 'Somayya Academy Dev');
      });

      test('Database name should be the dev database name', () {
        expect(AppConfig.databaseName, 'somayya_academy_dev.db');
      });

      test('Feature flags for dev should be set correctly', () {
        expect(AppConfig.enableDebugFeatures, isTrue);
        expect(AppConfig.enableDetailedLogging, isTrue);
        expect(AppConfig.enableAnalytics, isFalse);
        expect(AppConfig.enableCrashReporting, isFalse);
      });
    });

    group('Prod Flavor', () {
      setUp(() {
        AppConfig.setTestFlavor(AppFlavor.prod);
      });

      test('isProd should be true and isDev should be false', () {
        expect(AppConfig.isProd, isTrue);
        expect(AppConfig.isDev, isFalse);
      });

      test('API base URL should be the prod URL', () {
        expect(AppConfig.apiBaseUrl, ApiConstants.prodBaseUrl);
      });

      test('App name should be the prod name', () {
        expect(AppConfig.appName, 'Somayya Academy');
      });

      test('Database name should be the prod database name', () {
        expect(AppConfig.databaseName, 'somayya_academy.db');
      });

      test('Feature flags for prod should be set correctly', () {
        expect(AppConfig.enableDebugFeatures, isFalse);
        expect(AppConfig.enableDetailedLogging, isFalse);
        expect(AppConfig.enableAnalytics, isTrue);
        expect(AppConfig.enableCrashReporting, isTrue);
      });
    });
  });
}
