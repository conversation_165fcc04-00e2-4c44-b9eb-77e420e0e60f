import 'package:flutter_test/flutter_test.dart';
import 'package:somayya_academy/app/core/models/api_response.dart';

// A simple model for testing generic data parsing
class TestModel {
  final String id;
  final String name;

  TestModel({required this.id, required this.name});

  factory TestModel.fromJson(Map<String, dynamic> json) {
    return TestModel(id: json['id'], name: json['name']);
  }
}

void main() {
  group('ApiResponse', () {
    test('from<PERSON><PERSON> creates a success response with data', () {
      final json = {
        'success': true,
        'message': 'Data fetched',
        'data': {'id': '1', 'name': 'Test'}
      };

      final response = ApiResponse.fromJson(json, (data) => TestModel.fromJson(data));

      expect(response.success, isTrue);
      expect(response.message, 'Data fetched');
      expect(response.hasData, isTrue);
      expect(response.data, isA<TestModel>());
      expect(response.data?.id, '1');
    });

    test('from<PERSON><PERSON> creates an error response', () {
      final json = {
        'success': false,
        'message': 'An error occurred',
        'error': {'code': 'TEST_ERROR', 'message': 'Something went wrong'}
      };

      final response = ApiResponse.fromJson(json, null);

      expect(response.success, isFalse);
      expect(response.hasError, isTrue);
      expect(response.error?.code, 'TEST_ERROR');
      expect(response.error?.message, 'Something went wrong');
    });
  });

  group('ApiError', () {
    test('fromJson creates an ApiError with validation errors', () {
      final json = {
        'code': 'VALIDATION_ERROR',
        'message': 'Input is invalid',
        'validationErrors': [
          {'field': 'email', 'message': 'Enter a valid email'}
        ]
      };

      final error = ApiError.fromJson(json);

      expect(error.code, 'VALIDATION_ERROR');
      expect(error.validationErrors, isNotNull);
      expect(error.validationErrors?.first.field, 'email');
    });
  });

  group('PaginatedResponse', () {
    test('fromJson creates a paginated response', () {
      final json = {
        'items': [
          {'id': '1', 'name': 'Item 1'},
          {'id': '2', 'name': 'Item 2'}
        ],
        'totalItems': 2,
        'currentPage': 1,
        'totalPages': 1,
        'pageSize': 10
      };

      final response = PaginatedResponse.fromJson(json, (item) => TestModel.fromJson(item));

      expect(response.hasItems, isTrue);
      expect(response.items.length, 2);
      expect(response.items.first.name, 'Item 1');
      expect(response.totalItems, 2);
      expect(response.isFirstPage, isTrue);
      expect(response.isLastPage, isTrue);
    });
  });
}
