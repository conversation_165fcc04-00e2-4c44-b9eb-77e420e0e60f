import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:somayya_academy/app/core/config/app_config.dart';
import 'package:somayya_academy/app/core/network/services/api_service.dart';

import 'api_service_test.mocks.dart';

// This will generate a mock_api_service.dart file in the same directory
@GenerateMocks([FlutterSecureStorage])
void main() {
  late Dio dio;
  late DioAdapter dioAdapter;
  late MockFlutterSecureStorage mockStorage;
  late ApiService apiService;

  // General setup for mocks and dio
  setUp(() {
    dio = Dio();
    dio.interceptors.clear();
    dioAdapter = DioAdapter(dio: dio);
    mockStorage = MockFlutterSecureStorage();
    // Default stub for token reads to prevent MissingStubError from AuthInterceptor
    when(mockStorage.read(key: anyNamed('key'))).thenAnswer((_) async => null);
  });

  tearDown(() {
    AppConfig.resetFlavor();
  });

  group('ApiService Initialization', () {
    setUp(() {
      // Initialize inside the group to re-apply settings
      apiService = ApiService(dio: dio, storage: mockStorage);
    });

    test('sets up Dio with correct base URL and timeouts in Dev', () {
      AppConfig.setTestFlavor(AppFlavor.dev);
      apiService.onInit();

      expect(apiService.dio.options.baseUrl, AppConfig.apiBaseUrl);
      expect(
        apiService.dio.options.connectTimeout,
        const Duration(seconds: 30),
      );
    });

    test('adds logging interceptor in Dev mode', () {
      AppConfig.setTestFlavor(AppFlavor.dev);
      apiService.onInit();

      expect(
        apiService.dio.interceptors.any(
          (i) => i.runtimeType.toString() == 'EnhancedLogInterceptor',
        ),
        isTrue,
      );
    });

    test('does not add logging interceptor in Prod mode', () {
      AppConfig.setTestFlavor(AppFlavor.prod);
      apiService.onInit();

      expect(
        apiService.dio.interceptors.any(
          (i) => i.runtimeType.toString() == 'EnhancedLogInterceptor',
        ),
        isFalse,
      );
    });
  });

  group('Token Management', () {
    setUp(() {
      apiService = ApiService(dio: dio, storage: mockStorage);
      apiService.onInit();
    });

    test('setToken writes to secure storage', () async {
      const token = 'test_token';
      when(
        mockStorage.write(key: 'auth_token', value: token),
      ).thenAnswer((_) async => Future.value());

      await apiService.setToken(token);

      verify(mockStorage.write(key: 'auth_token', value: token)).called(1);
    });

    test('getToken reads from secure storage', () async {
      const token = 'test_token';
      when(mockStorage.read(key: 'auth_token')).thenAnswer((_) async => token);

      final result = await apiService.getToken();

      expect(result, token);
      verify(mockStorage.read(key: 'auth_token')).called(1);
    });

    test('clearToken deletes both tokens from secure storage', () async {
      when(
        mockStorage.delete(key: 'auth_token'),
      ).thenAnswer((_) async => Future.value());
      when(
        mockStorage.delete(key: 'refresh_token'),
      ).thenAnswer((_) async => Future.value());

      await apiService.clearToken();

      verify(mockStorage.delete(key: 'auth_token')).called(1);
      verify(mockStorage.delete(key: 'refresh_token')).called(1);
    });
  });

  group('Safe API Calls', () {
    const path = '/test';
    final successData = {'message': 'Success'};
    final successResponse = {'success': true, 'data': successData};

    setUp(() {
      apiService = ApiService(dio: dio, storage: mockStorage);
      apiService.onInit();
    });

    test('safeGet returns ApiResponse.success on 200', () async {
      dioAdapter.onGet(path, (server) => server.reply(200, successData));

      final response = await apiService.safeGet(path);

      expect(response.success, isTrue);
      expect(response.data, successData);
    });

    test('safePost returns ApiResponse.success on 201', () async {
      // The _handleResponse method will now correctly parse this structure
      dioAdapter.onPost(
        path,
        (server) => server.reply(201, successResponse),
        data: {}, // Match the exact empty data object
      );

      final response = await apiService.safePost(path, data: {});

      expect(response.success, isTrue);
      expect(response.data, successData);
    });

    test('safeGet returns ApiResponse.error on 404', () async {
      dioAdapter.onGet(
        path,
        (server) => server.reply(404, {'error': 'Not Found'}),
      );

      final response = await apiService.safeGet(path);

      expect(response.hasError, isTrue);
      expect(response.error?.code, 'NOT_FOUND');
    });

    test('safeGet returns ApiResponse.error on network error', () async {
      dioAdapter.onGet(
        path,
        (server) => server.throws(
          404,
          DioException(requestOptions: RequestOptions(path: path)),
        ),
      );

      final response = await apiService.safeGet(path);

      expect(response.hasError, isTrue);
      expect(response.error?.code, 'UNKNOWN_ERROR');
      expect(response.error?.message, 'An unknown error occurred');
    });

    test('safeGet uses fromJson when provided', () async {
      final jsonData = {'id': 1, 'name': 'Test'};
      dioAdapter.onGet(path, (server) => server.reply(200, jsonData));

      final response = await apiService.safeGet(
        path,
        fromJson: (json) => TestModel.fromJson(json as Map<String, dynamic>),
      );

      expect(response.success, isTrue);
      expect(response.data, isA<TestModel>());
      expect(response.data?.id, 1);
    });
  });
}

// Dummy model for testing fromJson functionality
class TestModel {
  final int id;
  final String name;

  TestModel({required this.id, required this.name});

  factory TestModel.fromJson(Map<String, dynamic> json) {
    return TestModel(id: json['id'], name: json['name']);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TestModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name;

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}
