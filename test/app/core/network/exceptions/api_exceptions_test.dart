import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:somayya_academy/app/core/network/exceptions/api_exceptions.dart';

import 'api_exceptions_test.mocks.dart';

@GenerateMocks([DioException, Response])
void main() {
  group('ApiExceptionHandler', () {
    late MockDioException mockDioException;
    late MockResponse mockResponse;

    setUp(() {
      mockDioException = MockDioException();
      mockResponse = MockResponse();
      when(mockDioException.response).thenReturn(mockResponse);
      when(mockResponse.data).thenReturn(null);
    });

    test('handleDioError returns NetworkException for connectionTimeout', () {
      when(mockDioException.type).thenReturn(DioExceptionType.connectionTimeout);
      final exception = ApiExceptionHandler.handleDioError(mockDioException);
      expect(exception, isA<NetworkException>());
      expect(exception.message, 'Request timeout');
    });

    test('handleDioError returns NetworkException for connectionError', () {
      when(mockDioException.type).thenReturn(DioExceptionType.connectionError);
      final exception = ApiExceptionHandler.handleDioError(mockDioException);
      expect(exception, isA<NetworkException>());
      expect(exception.message, 'Network connection error');
    });

    test('handleDioError returns ClientException for 401 Unauthorized', () {
      when(mockDioException.type).thenReturn(DioExceptionType.badResponse);
      when(mockResponse.statusCode).thenReturn(401);
      final exception = ApiExceptionHandler.handleDioError(mockDioException);
      expect(exception, isA<ClientException>());
      expect(exception.statusCode, 401);
    });

    test('handleDioError returns ServerException for 500 Internal Server Error', () {
      when(mockDioException.type).thenReturn(DioExceptionType.badResponse);
      when(mockResponse.statusCode).thenReturn(500);
      final exception = ApiExceptionHandler.handleDioError(mockDioException);
      expect(exception, isA<ServerException>());
      expect(exception.statusCode, 500);
    });

    test('handleDioError returns ValidationException for 400 with validation errors', () {
      when(mockDioException.type).thenReturn(DioExceptionType.badResponse);
      when(mockResponse.statusCode).thenReturn(400);
      when(mockResponse.data).thenReturn({
        'message': 'Validation failed',
        'validationErrors': [
          {'field': 'password', 'message': 'Password is too short'}
        ]
      });

      final exception = ApiExceptionHandler.handleDioError(mockDioException);
      expect(exception, isA<ValidationException>());
      final validationException = exception as ValidationException;
      expect(validationException.validationErrors.first.field, 'password');
    });

    test('handleDioError returns AuthException for 401 with specific error code', () {
      when(mockDioException.type).thenReturn(DioExceptionType.badResponse);
      when(mockResponse.statusCode).thenReturn(401);
      when(mockResponse.data).thenReturn({
        'code': 'TOKEN_EXPIRED',
        'message': 'Token has expired',
      });

      final exception = ApiExceptionHandler.handleDioError(mockDioException);
      expect(exception, isA<AuthException>());
      expect(exception.code, 'TOKEN_EXPIRED');
    });

    test('handleGenericError returns the same ApiException if passed one', () {
      final originalException = NetworkException.noInternet();
      final exception = ApiExceptionHandler.handleGenericError(originalException);
      expect(exception, same(originalException));
    });

    test('handleGenericError returns UnknownException for a non-Dio error', () {
      final originalException = Exception('A generic error');
      final exception = ApiExceptionHandler.handleGenericError(originalException);
      expect(exception, isA<UnknownException>());
      expect(exception.originalError, same(originalException));
    });
  });
}
