import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:somayya_academy/app/bindings/initial_binding.dart';
import 'package:somayya_academy/app/core/network/services/api_service.dart';
import 'package:somayya_academy/app/features/auth/services/auth_service.dart';
import 'package:somayya_academy/app/features/auth/services/firebase_auth_service.dart';
import 'package:somayya_academy/app/features/auth/services/social_auth_service.dart';

import '../../helpers/firebase_helpers.dart';

void main() {
  // Setup Firebase mocks before any tests run
  setUpAll(() {
    setupFirebaseCoreMocks();
  });

  group('InitialBinding', () {
    // Instantiate the binding
    final binding = InitialBinding();

    // Setup dependencies before each test
    setUp(() {
      binding.dependencies();
    });

    // Reset GetX container after each test to ensure isolation
    tearDown(() {
      Get.reset();
    });

    test('ApiService is registered correctly', () {
      // Assert that ApiService is registered as a singleton
      expect(Get.isRegistered<ApiService>(), isTrue);

      // Assert that we can find the instance
      final apiService = Get.find<ApiService>();
      expect(apiService, isA<ApiService>());
    });

    test('AuthService is registered correctly', () {
      // Assert that AuthService is registered as a singleton
      expect(Get.isRegistered<AuthService>(), isTrue);

      // Assert that we can find the instance
      final authService = Get.find<AuthService>();
      expect(authService, isA<AuthService>());
    });

    test('FirebaseAuthService is registered lazily', () {
      // Assert that FirebaseAuthService is registered
      expect(Get.isRegistered<FirebaseAuthService>(), isTrue);

      // Assert that it is not yet created (lazy)
      expect(Get.isPrepared<FirebaseAuthService>(), isTrue);

      // Find the instance, which should create it
      final firebaseAuthService = Get.find<FirebaseAuthService>();
      expect(firebaseAuthService, isA<FirebaseAuthService>());

      // Assert that it is now created
      expect(Get.isPrepared<FirebaseAuthService>(), isFalse);
    });

    test('SocialAuthService is registered lazily', () {
      // Assert that SocialAuthService is registered
      expect(Get.isRegistered<SocialAuthService>(), isTrue);

      // Assert that it is not yet created (lazy)
      expect(Get.isPrepared<SocialAuthService>(), isTrue);

      // Find the instance, which should create it
      final socialAuthService = Get.find<SocialAuthService>();
      expect(socialAuthService, isA<SocialAuthService>());

      // Assert that it is now created
      expect(Get.isPrepared<SocialAuthService>(), isFalse);
    });
  });
}
