// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in somayya_academy/test/app/features/auth/controllers/auth_controller_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:get/get.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;
import 'package:somayya_academy/app/core/models/api_response.dart' as _i3;
import 'package:somayya_academy/app/core/utils/password_encryptor.dart' as _i7;
import 'package:somayya_academy/app/features/auth/services/auth_service.dart'
    as _i4;
import 'package:somayya_academy/app/features/auth/services/social_auth_service.dart'
    as _i6;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeInternalFinalCallback_0<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeApiResponse_1<T> extends _i1.SmartFake
    implements _i3.ApiResponse<T> {
  _FakeApiResponse_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i4.AuthService {
  @override
  _i2.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>> validateOtp({
    required String? email,
    required String? otp,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#validateOtp, [], {#email: email, #otp: otp}),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#validateOtp, [], {
                      #email: email,
                      #otp: otp,
                    }),
                  ),
                ),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#validateOtp, [], {
                      #email: email,
                      #otp: otp,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>> updatePassword({
    required String? currentPassword,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePassword, [], {
              #currentPassword: currentPassword,
              #newPassword: newPassword,
            }),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#updatePassword, [], {
                      #currentPassword: currentPassword,
                      #newPassword: newPassword,
                    }),
                  ),
                ),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#updatePassword, [], {
                      #currentPassword: currentPassword,
                      #newPassword: newPassword,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>> signup({
    required String? name,
    required String? email,
    required String? password,
    Map<String, dynamic>? additionalData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signup, [], {
              #name: name,
              #email: email,
              #password: password,
              #additionalData: additionalData,
            }),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#signup, [], {
                      #name: name,
                      #email: email,
                      #password: password,
                      #additionalData: additionalData,
                    }),
                  ),
                ),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#signup, [], {
                      #name: name,
                      #email: email,
                      #password: password,
                      #additionalData: additionalData,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>> signin({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#signin, [], {
              #email: email,
              #password: password,
            }),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#signin, [], {
                      #email: email,
                      #password: password,
                    }),
                  ),
                ),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#signin, [], {
                      #email: email,
                      #password: password,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>> socialSignin({
    required String? email,
    required String? providerName,
    required String? socialUserId,
    String? device,
    String? ipAddress,
    String? location,
    String? fcmToken,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#socialSignin, [], {
              #email: email,
              #providerName: providerName,
              #socialUserId: socialUserId,
              #device: device,
              #ipAddress: ipAddress,
              #location: location,
              #fcmToken: fcmToken,
            }),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#socialSignin, [], {
                      #email: email,
                      #providerName: providerName,
                      #socialUserId: socialUserId,
                      #device: device,
                      #ipAddress: ipAddress,
                      #location: location,
                      #fcmToken: fcmToken,
                    }),
                  ),
                ),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#socialSignin, [], {
                      #email: email,
                      #providerName: providerName,
                      #socialUserId: socialUserId,
                      #device: device,
                      #ipAddress: ipAddress,
                      #location: location,
                      #fcmToken: fcmToken,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>> setPassword({
    required String? email,
    required String? password,
    required String? otp,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#setPassword, [], {
              #email: email,
              #password: password,
              #otp: otp,
            }),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#setPassword, [], {
                      #email: email,
                      #password: password,
                      #otp: otp,
                    }),
                  ),
                ),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#setPassword, [], {
                      #email: email,
                      #password: password,
                      #otp: otp,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>> resetPassword({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#resetPassword, [], {#email: email}),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#resetPassword, [], {#email: email}),
                  ),
                ),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#resetPassword, [], {#email: email}),
                  ),
                ),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>> sendEmailVerification({
    required String? email,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#sendEmailVerification, [], {#email: email}),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#sendEmailVerification, [], {
                      #email: email,
                    }),
                  ),
                ),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#sendEmailVerification, [], {
                      #email: email,
                    }),
                  ),
                ),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>> signout() =>
      (super.noSuchMethod(
            Invocation.method(#signout, []),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#signout, []),
                  ),
                ),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#signout, []),
                  ),
                ),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>);

  @override
  _i5.Future<bool> isAuthenticated() =>
      (super.noSuchMethod(
            Invocation.method(#isAuthenticated, []),
            returnValue: _i5.Future<bool>.value(false),
            returnValueForMissingStub: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<String?> getCurrentToken() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentToken, []),
            returnValue: _i5.Future<String?>.value(),
            returnValueForMissingStub: _i5.Future<String?>.value(),
          )
          as _i5.Future<String?>);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>> refreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#refreshToken, []),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#refreshToken, []),
                  ),
                ),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>.value(
                  _FakeApiResponse_1<Map<String, dynamic>>(
                    this,
                    Invocation.method(#refreshToken, []),
                  ),
                ),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>>);

  @override
  _i5.Future<void> clearAuthData() =>
      (super.noSuchMethod(
            Invocation.method(#clearAuthData, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [SocialAuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSocialAuthService extends _i1.Mock implements _i6.SocialAuthService {
  @override
  _i2.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?> signInWithGoogle() =>
      (super.noSuchMethod(
            Invocation.method(#signInWithGoogle, []),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?>.value(),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?>.value(),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?>);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?> signInWithFacebook() =>
      (super.noSuchMethod(
            Invocation.method(#signInWithFacebook, []),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?>.value(),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?>.value(),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?>);

  @override
  _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?> signInWithMicrosoft() =>
      (super.noSuchMethod(
            Invocation.method(#signInWithMicrosoft, []),
            returnValue:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?>.value(),
            returnValueForMissingStub:
                _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?>.value(),
          )
          as _i5.Future<_i3.ApiResponse<Map<String, dynamic>>?>);

  @override
  _i5.Future<void> logout() =>
      (super.noSuchMethod(
            Invocation.method(#logout, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [PasswordEncryptor].
///
/// See the documentation for Mockito's code generation for more information.
class MockPasswordEncryptor extends _i1.Mock implements _i7.PasswordEncryptor {
  @override
  _i5.Future<String> encryptPassword(
    String? plainText, {
    String? keyStr = 'somayyaacademy',
  }) =>
      (super.noSuchMethod(
            Invocation.method(#encryptPassword, [plainText], {#keyStr: keyStr}),
            returnValue: _i5.Future<String>.value(
              _i8.dummyValue<String>(
                this,
                Invocation.method(
                  #encryptPassword,
                  [plainText],
                  {#keyStr: keyStr},
                ),
              ),
            ),
            returnValueForMissingStub: _i5.Future<String>.value(
              _i8.dummyValue<String>(
                this,
                Invocation.method(
                  #encryptPassword,
                  [plainText],
                  {#keyStr: keyStr},
                ),
              ),
            ),
          )
          as _i5.Future<String>);
}
