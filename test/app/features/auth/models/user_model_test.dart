import 'package:flutter_test/flutter_test.dart';
import 'package:somayya_academy/app/features/auth/models/user_model.dart';

void main() {
  group('User Model', () {
    const testId = '123';
    const testName = '<PERSON>';
    const testEmail = '<EMAIL>';
    const testToken = 'abc123token';

    group('Constructor', () {
      test('creates User with all required fields', () {
        // Act
        final user = User(
          id: testId,
          name: testName,
          email: testEmail,
          token: testToken,
        );

        // Assert
        expect(user.id, equals(testId));
        expect(user.name, equals(testName));
        expect(user.email, equals(testEmail));
        expect(user.token, equals(testToken));
      });
    });

    group('fromJson', () {
      test('creates User from valid JSON with all fields', () {
        // Arrange
        final json = {
          'id': testId,
          'name': testName,
          'email': testEmail,
          'token': testToken,
        };

        // Act
        final user = User.fromJson(json);

        // Assert
        expect(user.id, equals(testId));
        expect(user.name, equals(testName));
        expect(user.email, equals(testEmail));
        expect(user.token, equals(testToken));
      });

      test('creates User from JSON with missing optional fields', () {
        // Arrange
        final json = {
          'id': testId,
          'email': testEmail,
        };

        // Act
        final user = User.fromJson(json);

        // Assert
        expect(user.id, equals(testId));
        expect(user.name, equals(''));
        expect(user.email, equals(testEmail));
        expect(user.token, equals(''));
      });

      test('creates User from JSON with null values', () {
        // Arrange
        final json = {
          'id': null,
          'name': null,
          'email': null,
          'token': null,
        };

        // Act
        final user = User.fromJson(json);

        // Assert
        expect(user.id, equals(''));
        expect(user.name, equals(''));
        expect(user.email, equals(''));
        expect(user.token, equals(''));
      });

      test('creates User from JSON with numeric id', () {
        // Arrange
        final json = {
          'id': 123, // Numeric ID
          'name': testName,
          'email': testEmail,
          'token': testToken,
        };

        // Act
        final user = User.fromJson(json);

        // Assert
        expect(user.id, equals('123')); // Should be converted to string
        expect(user.name, equals(testName));
        expect(user.email, equals(testEmail));
        expect(user.token, equals(testToken));
      });

      test('creates User from empty JSON', () {
        // Arrange
        final json = <String, dynamic>{};

        // Act
        final user = User.fromJson(json);

        // Assert
        expect(user.id, equals(''));
        expect(user.name, equals(''));
        expect(user.email, equals(''));
        expect(user.token, equals(''));
      });

      test('handles JSON with extra fields gracefully', () {
        // Arrange
        final json = {
          'id': testId,
          'name': testName,
          'email': testEmail,
          'token': testToken,
          'extraField': 'extraValue',
          'anotherField': 123,
        };

        // Act
        final user = User.fromJson(json);

        // Assert
        expect(user.id, equals(testId));
        expect(user.name, equals(testName));
        expect(user.email, equals(testEmail));
        expect(user.token, equals(testToken));
      });
    });

    group('toJson', () {
      test('converts User to JSON with all fields', () {
        // Arrange
        final user = User(
          id: testId,
          name: testName,
          email: testEmail,
          token: testToken,
        );

        // Act
        final json = user.toJson();

        // Assert
        expect(json, equals({
          'id': testId,
          'name': testName,
          'email': testEmail,
          'token': testToken,
        }));
      });

      test('converts User with empty fields to JSON', () {
        // Arrange
        final user = User(
          id: '',
          name: '',
          email: '',
          token: '',
        );

        // Act
        final json = user.toJson();

        // Assert
        expect(json, equals({
          'id': '',
          'name': '',
          'email': '',
          'token': '',
        }));
      });
    });

    group('JSON Roundtrip', () {
      test('fromJson and toJson are consistent', () {
        // Arrange
        final originalJson = {
          'id': testId,
          'name': testName,
          'email': testEmail,
          'token': testToken,
        };

        // Act
        final user = User.fromJson(originalJson);
        final resultJson = user.toJson();

        // Assert
        expect(resultJson, equals(originalJson));
      });

      test('multiple roundtrips maintain data integrity', () {
        // Arrange
        final originalUser = User(
          id: testId,
          name: testName,
          email: testEmail,
          token: testToken,
        );

        // Act - Multiple roundtrips
        final json1 = originalUser.toJson();
        final user1 = User.fromJson(json1);
        final json2 = user1.toJson();
        final user2 = User.fromJson(json2);

        // Assert
        expect(user2.id, equals(originalUser.id));
        expect(user2.name, equals(originalUser.name));
        expect(user2.email, equals(originalUser.email));
        expect(user2.token, equals(originalUser.token));
      });
    });

    group('Equality and Comparison', () {
      test('two Users with same data should have same properties', () {
        // Arrange
        final user1 = User(
          id: testId,
          name: testName,
          email: testEmail,
          token: testToken,
        );
        final user2 = User(
          id: testId,
          name: testName,
          email: testEmail,
          token: testToken,
        );

        // Assert
        expect(user1.id, equals(user2.id));
        expect(user1.name, equals(user2.name));
        expect(user1.email, equals(user2.email));
        expect(user1.token, equals(user2.token));
      });

      test('Users with different data should have different properties', () {
        // Arrange
        final user1 = User(
          id: testId,
          name: testName,
          email: testEmail,
          token: testToken,
        );
        final user2 = User(
          id: 'different-id',
          name: 'Different Name',
          email: '<EMAIL>',
          token: 'different-token',
        );

        // Assert
        expect(user1.id, isNot(equals(user2.id)));
        expect(user1.name, isNot(equals(user2.name)));
        expect(user1.email, isNot(equals(user2.email)));
        expect(user1.token, isNot(equals(user2.token)));
      });
    });
  });
}
