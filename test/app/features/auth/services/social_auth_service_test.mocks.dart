// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in somayya_academy/test/app/features/auth/services/social_auth_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:get/get.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:somayya_academy/app/core/models/api_response.dart' as _i5;
import 'package:somayya_academy/app/features/auth/services/social_auth_service.dart'
    as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeInternalFinalCallback_0<T> extends _i1.SmartFake
    implements _i2.InternalFinalCallback<T> {
  _FakeInternalFinalCallback_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [SocialAuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSocialAuthService extends _i1.Mock implements _i3.SocialAuthService {
  @override
  _i2.InternalFinalCallback<void> get onStart =>
      (super.noSuchMethod(
            Invocation.getter(#onStart),
            returnValue: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onStart),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onStart),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  _i2.InternalFinalCallback<void> get onDelete =>
      (super.noSuchMethod(
            Invocation.getter(#onDelete),
            returnValue: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onDelete),
            ),
            returnValueForMissingStub: _FakeInternalFinalCallback_0<void>(
              this,
              Invocation.getter(#onDelete),
            ),
          )
          as _i2.InternalFinalCallback<void>);

  @override
  bool get initialized =>
      (super.noSuchMethod(
            Invocation.getter(#initialized),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  bool get isClosed =>
      (super.noSuchMethod(
            Invocation.getter(#isClosed),
            returnValue: false,
            returnValueForMissingStub: false,
          )
          as bool);

  @override
  _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?> signInWithGoogle() =>
      (super.noSuchMethod(
            Invocation.method(#signInWithGoogle, []),
            returnValue:
                _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?>.value(),
            returnValueForMissingStub:
                _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?>.value(),
          )
          as _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?>);

  @override
  _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?> signInWithFacebook() =>
      (super.noSuchMethod(
            Invocation.method(#signInWithFacebook, []),
            returnValue:
                _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?>.value(),
            returnValueForMissingStub:
                _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?>.value(),
          )
          as _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?>);

  @override
  _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?> signInWithMicrosoft() =>
      (super.noSuchMethod(
            Invocation.method(#signInWithMicrosoft, []),
            returnValue:
                _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?>.value(),
            returnValueForMissingStub:
                _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?>.value(),
          )
          as _i4.Future<_i5.ApiResponse<Map<String, dynamic>>?>);

  @override
  _i4.Future<void> logout() =>
      (super.noSuchMethod(
            Invocation.method(#logout, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void onInit() => super.noSuchMethod(
    Invocation.method(#onInit, []),
    returnValueForMissingStub: null,
  );

  @override
  void onReady() => super.noSuchMethod(
    Invocation.method(#onReady, []),
    returnValueForMissingStub: null,
  );

  @override
  void onClose() => super.noSuchMethod(
    Invocation.method(#onClose, []),
    returnValueForMissingStub: null,
  );

  @override
  void $configureLifeCycle() => super.noSuchMethod(
    Invocation.method(#$configureLifeCycle, []),
    returnValueForMissingStub: null,
  );
}
