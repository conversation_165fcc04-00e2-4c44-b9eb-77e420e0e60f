import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import 'package:somayya_academy/app/core/constants/api_constants.dart';
import 'package:somayya_academy/app/core/network/services/api_service.dart';
import 'package:somayya_academy/app/features/auth/services/auth_service.dart';

import '../../../../helpers/firebase_helpers.dart';

void main() {
  group('AuthService', () {
    late AuthService authService;
    late ApiService apiService;
    late DioAdapter dioAdapter;

    setUp(() {
      // Setup Firebase mocks
      setupFirebaseCoreMocks();

      // Clear GetX dependencies
      Get.reset();

      // Setup API service with mock adapter
      apiService = ApiService();
      dioAdapter = DioAdapter(dio: apiService.dio);
      Get.put<ApiService>(apiService);

      authService = AuthService();
    });

    tearDown(() {
      Get.reset();
    });

    group('validateOtp', () {
      test('returns success response when OTP is valid', () async {
        // Arrange
        const email = '<EMAIL>';
        const otp = '123456';
        final responseData = {'valid': true, 'message': 'OTP validated'};

        dioAdapter.onPost(
          ApiConstants.validateOtp,
          (server) =>
              server.reply(200, {'success': true, 'data': responseData}),
          data: {'email': email, 'otp': otp},
        );

        // Act
        final result = await authService.validateOtp(email: email, otp: otp);

        // Assert
        expect(result.success, isTrue);
        expect(result.data, equals(responseData));
      });

      test('returns error response when OTP is invalid', () async {
        // Arrange
        const email = '<EMAIL>';
        const otp = '000000';

        dioAdapter.onPost(
          ApiConstants.validateOtp,
          (server) => server.reply(400, {
            'success': false,
            'error': {'message': 'Invalid OTP'},
          }),
          data: {'email': email, 'otp': otp},
        );

        // Act
        final result = await authService.validateOtp(email: email, otp: otp);

        // Assert
        expect(result.success, isFalse);
        expect(result.error, isNotNull);
      });
    });

    group('setPassword', () {
      test(
        'returns success response when password is set successfully',
        () async {
          // Arrange
          const email = '<EMAIL>';
          const otp = '123456';
          const password = 'newPassword123';
          final responseData = {'message': 'Password set successfully'};

          dioAdapter.onPost(
            ApiConstants.setPassword,
            (server) =>
                server.reply(200, {'success': true, 'data': responseData}),
            data: {'email': email, 'otp': otp, 'password': password},
          );

          // Act
          final result = await authService.setPassword(
            email: email,
            otp: otp,
            password: password,
          );

          // Assert
          expect(result.success, isTrue);
          expect(result.data, equals(responseData));
        },
      );

      test('returns error response when OTP is expired', () async {
        // Arrange
        const email = '<EMAIL>';
        const otp = '123456';
        const password = 'newPassword123';

        dioAdapter.onPost(
          ApiConstants.setPassword,
          (server) => server.reply(400, {
            'success': false,
            'error': {'message': 'OTP expired'},
          }),
          data: {'email': email, 'otp': otp, 'password': password},
        );

        // Act
        final result = await authService.setPassword(
          email: email,
          otp: otp,
          password: password,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.error?.message, equals('OTP expired'));
      });
    });

    group('signin', () {
      test(
        'stores tokens and returns success response on successful signin',
        () async {
          // Arrange
          const email = '<EMAIL>';
          const password = 'password123';
          const token = 'access_token_123';
          const refreshToken = 'refresh_token_123';
          final responseData = {
            'token': token,
            'refreshToken': refreshToken,
            'user': {'id': '1', 'email': email, 'name': 'Test User'},
          };

          dioAdapter.onPost(
            ApiConstants.signin,
            (server) =>
                server.reply(200, {'success': true, 'data': responseData}),
            data: {'email': email, 'password': password},
          );

          // Act
          final result = await authService.signin(
            email: email,
            password: password,
          );

          // Assert
          expect(result.success, isTrue);
          expect(result.data, equals(responseData));
          // Note: Token storage verification would require mocking secure storage
        },
      );

      test('returns error response on invalid credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrongpassword';

        dioAdapter.onPost(
          ApiConstants.signin,
          (server) => server.reply(401, {
            'success': false,
            'error': {'message': 'Invalid credentials'},
          }),
          data: {'email': email, 'password': password},
        );

        // Act
        final result = await authService.signin(
          email: email,
          password: password,
        );

        // Assert
        expect(result.success, isFalse);
        expect(result.error?.message, equals('Invalid credentials'));
      });
    });

    group('socialSignin', () {
      test(
        'stores tokens and returns success response on successful social signin',
        () async {
          // Arrange
          const email = '<EMAIL>';
          const providerName = 'google';
          const socialUserId = 'google_123';
          const token = 'access_token_123';
          const refreshToken = 'refresh_token_123';
          final responseData = {
            'token': token,
            'refreshToken': refreshToken,
            'user': {'id': '1', 'email': email, 'name': 'Test User'},
          };

          dioAdapter.onPost(
            ApiConstants.signin,
            (server) =>
                server.reply(200, {'success': true, 'data': responseData}),
            data: {
              'email': email,
              'providerName': providerName,
              'socialUserId': socialUserId,
            },
          );

          // Act
          final result = await authService.socialSignin(
            email: email,
            providerName: providerName,
            socialUserId: socialUserId,
          );

          // Assert
          expect(result.success, isTrue);
          expect(result.data, equals(responseData));
        },
      );

      test('filters out null values from request data', () async {
        // Arrange
        const email = '<EMAIL>';
        const providerName = 'google';
        const socialUserId = 'google_123';
        final responseData = {'message': 'Success'};

        dioAdapter.onPost(
          ApiConstants.signin,
          (server) =>
              server.reply(200, {'success': true, 'data': responseData}),
          data: {
            'email': email,
            'providerName': providerName,
            'socialUserId': socialUserId,
            // Note: null values should be filtered out
          },
        );

        // Act
        final result = await authService.socialSignin(
          email: email,
          providerName: providerName,
          socialUserId: socialUserId,
          device: null, // Should be filtered out
          ipAddress: null, // Should be filtered out
          location: null, // Should be filtered out
          fcmToken: null, // Should be filtered out
        );

        // Assert
        expect(result.success, isTrue);
        expect(result.data, equals(responseData));
      });
    });

    group('sendEmailVerification', () {
      test(
        'returns success response when email verification is sent',
        () async {
          // Arrange
          const email = '<EMAIL>';
          final responseData = {'message': 'Verification email sent'};

          dioAdapter.onPost(
            ApiConstants.emailVerification,
            (server) =>
                server.reply(200, {'success': true, 'data': responseData}),
            data: {'email': email},
          );

          // Act
          final result = await authService.sendEmailVerification(email: email);

          // Assert
          expect(result.success, isTrue);
          expect(result.data, equals(responseData));
        },
      );

      test('returns error response when email is invalid', () async {
        // Arrange
        const email = 'invalid-email';

        dioAdapter.onPost(
          ApiConstants.emailVerification,
          (server) => server.reply(400, {
            'success': false,
            'error': {'message': 'Invalid email format'},
          }),
          data: {'email': email},
        );

        // Act
        final result = await authService.sendEmailVerification(email: email);

        // Assert
        expect(result.success, isFalse);
        expect(result.error?.message, equals('Invalid email format'));
      });
    });

    group('signout', () {
      test('clears tokens and returns success response', () async {
        // Arrange
        final responseData = {'message': 'Signed out successfully'};

        dioAdapter.onGet(
          ApiConstants.signout,
          (server) =>
              server.reply(200, {'success': true, 'data': responseData}),
        );

        // Act
        final result = await authService.signout();

        // Assert
        expect(result.success, isTrue);
        expect(result.data, equals(responseData));
        // Note: Token clearing verification would require mocking secure storage
      });

      test('clears tokens even when API call fails', () async {
        // Arrange
        dioAdapter.onGet(
          ApiConstants.signout,
          (server) => server.reply(500, {
            'success': false,
            'error': {'message': 'Server error'},
          }),
        );

        // Act
        final result = await authService.signout();

        // Assert
        expect(result.success, isFalse);
        // Note: Token clearing should still happen regardless of API response
      });
    });

    group('refreshToken', () {
      test('stores new tokens and returns success response', () async {
        // Arrange - First set up a refresh token by doing a signin
        const email = '<EMAIL>';
        const password = 'password123';
        const initialRefreshToken = 'initial_refresh_token';

        // Mock signin to store initial refresh token
        dioAdapter.onPost(
          ApiConstants.signin,
          (server) => server.reply(200, {
            'success': true,
            'data': {
              'token': 'initial_token',
              'refreshToken': initialRefreshToken,
              'user': {'id': '1', 'email': email},
            },
          }),
          data: {'email': email, 'password': password},
        );

        // Perform signin to store refresh token
        await authService.signin(email: email, password: password);

        // Now set up the refresh token endpoint
        const newToken = 'new_access_token';
        const newRefreshToken = 'new_refresh_token';
        final responseData = {
          'token': newToken,
          'refreshToken': newRefreshToken,
        };

        dioAdapter.onPost(
          ApiConstants.refreshToken,
          (server) =>
              server.reply(200, {'success': true, 'data': responseData}),
          data: {'refreshToken': initialRefreshToken},
        );

        // Act
        final result = await authService.refreshToken();

        // Assert
        expect(result.success, isTrue);
        expect(result.data, equals(responseData));
      });

      test('returns error response when no refresh token is available', () async {
        // Arrange - No need to mock HTTP call since service checks for token first
        // The service will return error before making HTTP request

        // Act
        final result = await authService.refreshToken();

        // Assert
        expect(result.success, isFalse);
        expect(result.error?.message, equals('No refresh token available'));
      });
    });

    group('clearAuthData', () {
      test('clears authentication data', () async {
        // Act
        await authService.clearAuthData();

        // Assert
        // Note: This would require mocking secure storage to verify token clearing
        // For now, we just verify the method completes without error
        expect(true, isTrue); // Placeholder assertion
      });
    });
  });
}
