import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/annotations.dart';
import 'package:somayya_academy/app/core/network/services/api_service.dart';
import 'package:somayya_academy/app/features/auth/services/auth_service.dart';
import 'package:somayya_academy/app/features/auth/services/social_auth_service.dart';

import '../../../../helpers/firebase_helpers.dart';

// Generate mocks for dependencies
@GenerateNiceMocks([MockSpec<SocialAuthService>()])
void main() {
  group('SocialAuthService', () {
    late SocialAuthService socialAuthService;

    setUp(() {
      // Setup Firebase mocks
      setupFirebaseCoreMocks();

      // Clear GetX dependencies
      Get.reset();

      // Setup required dependencies
      final apiService = ApiService();
      Get.put<ApiService>(apiService);

      final authService = AuthService();
      Get.put<AuthService>(authService);

      socialAuthService = SocialAuthService();
    });

    tearDown(() {
      Get.reset();
    });

    group('signInWithGoogle', () {
      test(
        'returns success response with user data on successful Google sign-in',
        () async {
          // This test would require mocking Google Sign-In
          // For now, we'll test the service structure
          expect(socialAuthService, isNotNull);
          expect(socialAuthService.signInWithGoogle, isA<Function>());
        },
      );

      test('handles Google sign-in cancellation gracefully', () async {
        // Test structure - actual implementation would mock Google Sign-In
        expect(socialAuthService.signInWithGoogle, isA<Function>());
      });

      test('handles Google sign-in errors appropriately', () async {
        // Test structure - actual implementation would mock Google Sign-In
        expect(socialAuthService.signInWithGoogle, isA<Function>());
      });
    });

    group('signInWithFacebook', () {
      test(
        'returns success response with user data on successful Facebook sign-in',
        () async {
          // Test structure - actual implementation would mock Facebook Login
          expect(socialAuthService.signInWithFacebook, isA<Function>());
        },
      );

      test('handles Facebook sign-in cancellation gracefully', () async {
        // Test structure - actual implementation would mock Facebook Login
        expect(socialAuthService.signInWithFacebook, isA<Function>());
      });

      test('handles Facebook sign-in errors appropriately', () async {
        // Test structure - actual implementation would mock Facebook Login
        expect(socialAuthService.signInWithFacebook, isA<Function>());
      });
    });

    group('signInWithMicrosoft', () {
      test(
        'returns success response with user data on successful Microsoft sign-in',
        () async {
          // Test structure - actual implementation would mock Microsoft Authentication
          expect(socialAuthService.signInWithMicrosoft, isA<Function>());
        },
      );

      test('handles Microsoft sign-in cancellation gracefully', () async {
        // Test structure - actual implementation would mock Microsoft Authentication
        expect(socialAuthService.signInWithMicrosoft, isA<Function>());
      });

      test('handles Microsoft sign-in errors appropriately', () async {
        // Test structure - actual implementation would mock Microsoft Authentication
        expect(socialAuthService.signInWithMicrosoft, isA<Function>());
      });
    });

    group('logout', () {
      test('successfully logs out from all social providers', () async {
        // Act
        await socialAuthService.logout();

        // Assert - test completes without throwing
        expect(true, isTrue);
      });

      test('handles logout errors gracefully', () async {
        // Test structure - actual implementation would test error scenarios
        expect(socialAuthService.logout, isA<Function>());
      });
    });

    group('Integration with GetX', () {
      test('can be registered and retrieved from GetX', () {
        // Arrange & Act
        Get.put<SocialAuthService>(socialAuthService);
        final retrievedService = Get.find<SocialAuthService>();

        // Assert
        expect(retrievedService, isNotNull);
        expect(retrievedService, equals(socialAuthService));
      });

      test('maintains singleton behavior when registered with GetX', () {
        // Arrange & Act
        Get.put<SocialAuthService>(socialAuthService);
        final firstRetrieval = Get.find<SocialAuthService>();
        final secondRetrieval = Get.find<SocialAuthService>();

        // Assert
        expect(firstRetrieval, equals(secondRetrieval));
        expect(identical(firstRetrieval, secondRetrieval), isTrue);
      });
    });
  });
}
