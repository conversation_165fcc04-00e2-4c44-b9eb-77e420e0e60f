import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:somayya_academy/app/core/network/services/api_service.dart';
import 'package:somayya_academy/app/features/auth/bindings/auth_binding.dart';
import 'package:somayya_academy/app/features/auth/controllers/auth_controller.dart';
import 'package:somayya_academy/app/features/auth/services/auth_service.dart';
import 'package:somayya_academy/app/features/auth/services/social_auth_service.dart';

import '../../../../helpers/firebase_helpers.dart';

void main() {
  group('AuthBinding', () {
    late AuthBinding authBinding;

    setUp(() {
      // Setup Firebase mocks
      setupFirebaseCoreMocks();

      // Clear all GetX dependencies before each test
      Get.reset();

      // Register required dependencies
      Get.put<ApiService>(ApiService());
      Get.put<AuthService>(AuthService());
      Get.put<SocialAuthService>(SocialAuthService());

      authBinding = AuthBinding();
    });

    tearDown(() {
      // Clean up after each test
      Get.reset();
    });

    test('dependencies() registers AuthService as lazy singleton', () {
      // Act
      authBinding.dependencies();

      // Assert
      expect(Get.isRegistered<AuthService>(), isTrue);

      // Verify it's lazy - should not be instantiated yet
      expect(Get.find<AuthService>(), isA<AuthService>());
    });

    test('dependencies() registers AuthController as singleton', () {
      // Act
      authBinding.dependencies();

      // Assert
      expect(Get.isRegistered<AuthController>(), isTrue);
      expect(Get.find<AuthController>(), isA<AuthController>());
    });

    test('dependencies() registers all required services', () {
      // Act
      authBinding.dependencies();

      // Assert - Check that all expected services are registered
      expect(Get.isRegistered<AuthService>(), isTrue);
      expect(Get.isRegistered<AuthController>(), isTrue);
    });

    test('AuthService can be retrieved after binding', () {
      // Act
      authBinding.dependencies();
      final authService = Get.find<AuthService>();

      // Assert
      expect(authService, isNotNull);
      expect(authService, isA<AuthService>());
    });

    test('AuthController can be retrieved after binding', () {
      // Act
      authBinding.dependencies();
      final authController = Get.find<AuthController>();

      // Assert
      expect(authController, isNotNull);
      expect(authController, isA<AuthController>());
    });

    test('multiple calls to dependencies() do not cause conflicts', () {
      // Act
      authBinding.dependencies();
      authBinding.dependencies(); // Call again

      // Assert - Should still work without errors
      expect(Get.isRegistered<AuthService>(), isTrue);
      expect(Get.isRegistered<AuthController>(), isTrue);

      final authService1 = Get.find<AuthService>();
      final authService2 = Get.find<AuthService>();
      final authController1 = Get.find<AuthController>();
      final authController2 = Get.find<AuthController>();

      // Should return the same instances (singletons)
      expect(identical(authService1, authService2), isTrue);
      expect(identical(authController1, authController2), isTrue);
    });
  });
}
