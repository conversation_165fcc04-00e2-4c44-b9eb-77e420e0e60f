import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_core_platform_interface/firebase_core_platform_interface.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

// A mock FirebasePlatform implementation that handles app initialization and retrieval.
class MockFirebasePlatform extends Mock
    with MockPlatformInterfaceMixin
    implements FirebasePlatform {
  // A map to store the mocked Firebase app instances.
  final Map<String, FirebaseAppPlatform> _apps = {};

  @override
  Future<FirebaseAppPlatform> initializeApp({
    String? name,
    FirebaseOptions? options,
  }) async {
    final appName = name ?? '[DEFAULT]';
    final app = MockFirebaseApp(name: appName, options: options);
    _apps[appName] = app;
    return app;
  }

  @override
  FirebaseAppPlatform app([String name = '[DEFAULT]']) {
    // Before initializing, ensure a default app exists for lookups.
    if (_apps.isEmpty) {
      initializeApp();
    }
    return _apps[name]!;
  }

  @override
  List<FirebaseAppPlatform> get apps => _apps.values.toList();
}

class MockFirebaseApp extends Mock
    with MockPlatformInterfaceMixin
    implements FirebaseAppPlatform {
  final String _name;
  final FirebaseOptions _options;

  MockFirebaseApp({String? name, FirebaseOptions? options})
      : _name = name ?? '[DEFAULT]',
        _options = options ??
            const FirebaseOptions(
              apiKey: 'test-api-key',
              appId: 'test-app-id',
              messagingSenderId: 'test-sender-id',
              projectId: 'test-project-id',
            );

  @override
  String get name => _name;

  @override
  FirebaseOptions get options => _options;
}

// Helper to set up Firebase mocks for tests
void setupFirebaseCoreMocks() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mock = MockFirebasePlatform();
  Firebase.delegatePackingProperty = mock;
}

// Extended mock classes for capturing and failure simulation
class MockFirebasePlatformWithCapture extends Mock
    with MockPlatformInterfaceMixin
    implements FirebasePlatform {
  final Function(FirebaseOptions?) onInitialize;

  MockFirebasePlatformWithCapture(this.onInitialize);

  @override
  Future<FirebaseAppPlatform> initializeApp({
    String? name,
    FirebaseOptions? options,
  }) async {
    onInitialize(options);
    return MockFirebaseApp(name: name, options: options);
  }
}

class MockFirebasePlatformWithFailure extends Mock
    with MockPlatformInterfaceMixin
    implements FirebasePlatform {
  @override
  Future<FirebaseAppPlatform> initializeApp({
    String? name,
    FirebaseOptions? options,
  }) async {
    throw Exception('Firebase initialization failed');
  }
}

void setupFirebaseCoreMocksForCapture(Function(FirebaseOptions?) onInitialize) {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mock = MockFirebasePlatformWithCapture(onInitialize);
  Firebase.delegatePackingProperty = mock;
}

void setupFirebaseCoreMocksForFailure() {
  TestWidgetsFlutterBinding.ensureInitialized();
  final mock = MockFirebasePlatformWithFailure();
  Firebase.delegatePackingProperty = mock;
}
