PODS:
  - AppAuth (2.0.0):
    - AppAuth/Core (= 2.0.0)
    - AppAuth/ExternalUserAgent (= 2.0.0)
  - AppAuth/Core (2.0.0)
  - AppAuth/ExternalUserAgent (2.0.0):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FBAEMKit (18.0.0):
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit (18.0.0):
    - FBAEMKit (= 18.0.0)
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit_Basics (18.0.0)
  - FBSDKLoginKit (18.0.0):
    - FBSDKCoreKit (= 18.0.0)
  - Firebase/Auth (11.15.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 11.15.0)
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - firebase_auth (5.7.0):
    - Firebase/Auth (= 11.15.0)
    - firebase_core
    - Flutter
  - firebase_core (3.15.2):
    - Firebase/CoreOnly (= 11.15.0)
    - Flutter
  - FirebaseAppCheckInterop (11.15.0)
  - FirebaseAuth (11.15.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseAuthInterop (~> 11.0)
    - FirebaseCore (~> 11.15.0)
    - FirebaseCoreExtension (~> 11.15.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GTMSessionFetcher/Core (< 5.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (11.15.0)
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - Flutter (1.0.0)
  - flutter_facebook_auth (7.1.2):
    - FBSDKLoginKit (~> 18.0.0)
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 9.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleSignIn (9.0.0):
    - AppAuth (~> 2.0)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (~> 5.0)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (5.0.0):
    - AppAuth/Core (~> 2.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (101.0.0)

DEPENDENCIES:
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - PromisesObjC
    - RecaptchaInterop

EXTERNAL SOURCES:
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"

SPEC CHECKSUMS:
  AppAuth: 1c1a8afa7e12f2ec3a294d9882dfa5ab7d3cb063
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  FBAEMKit: e34530df538b8eb8aeb53c35867715ba6c63ef0c
  FBSDKCoreKit: d3f479a69127acebb1c6aad91c1a33907bcf6c2f
  FBSDKCoreKit_Basics: 017b6dc2a1862024815a8229e75661e627ac1e29
  FBSDKLoginKit: 5875762d1fe09ddcb05d03365d4f5dc34413843d
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  firebase_auth: 50af8366c87bb88c80ebeae62eb60189c7246b9b
  firebase_core: 995454a784ff288be5689b796deb9e9fa3601818
  FirebaseAppCheckInterop: 06fe5a3799278ae4667e6c432edd86b1030fa3df
  FirebaseAuth: a6575e5fbf46b046c58dc211a28a5fbdd8d4c83b
  FirebaseAuthInterop: 7087d7a4ee4bc4de019b2d0c240974ed5d89e2fd
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreExtension: edbd30474b5ccf04e5f001470bdf6ea616af2435
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_facebook_auth: d97dc09216aebd99d7ac52360186a630f67bdf18
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  google_sign_in_ios: 205742c688aea0e64db9da03c33121694a365109
  GoogleSignIn: c7f09cfbc85a1abf69187be091997c317cc33b77
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: 217a876b249c3c585a54fd6f73e6b58c4f5c4238
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba

PODFILE CHECKSUM: bcc93b722fe4e221e4e0c4d62e801ec654240f26

COCOAPODS: 1.16.2
